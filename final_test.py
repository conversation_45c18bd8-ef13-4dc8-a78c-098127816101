from playwright.sync_api import sync_playwright
import time

def final_verification():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # Login
        page.goto("http://localhost:8080/TatarepuestosV2/index.php")
        page.fill('input[name="username"]', 'admin')
        page.fill('input[name="password"]', 'admin123')
        page.click('button[type="submit"]')
        page.wait_for_selector('.dashboard')
        
        # Abrir cotizaciones
        page.click('#openQuoteBtn')
        time.sleep(2)
        
        # Test 1: Búsqueda de productos
        page.fill('#quoteProductSearch', 'filtro')
        time.sleep(3)
        
        results = page.query_selector('#quoteSearchResults .search-result-item')
        if results:
            print("✅ Búsqueda de productos funcionando")
            # Agregar producto
            page.click('#quoteSearchResults .search-result-item .action-button')
            time.sleep(1)
        else:
            print("❌ Búsqueda de productos no funciona")
        
        # Test 2: Guardar cotización
        page.fill('#quoteClientName', 'Cliente Test')
        page.fill('#quoteClientRut', '12345678-9')
        page.click('button[onclick="saveQuote()"]')
        time.sleep(3)
        
        # Test 3: Ver cotizaciones guardadas
        page.click('button[onclick="openQuotesList()"]')
        time.sleep(3)
        
        quotes_list = page.query_selector('#quotesListContainer .quote-list-item')
        if quotes_list:
            print("✅ Lista de cotizaciones funcionando")
        else:
            print("❌ Lista de cotizaciones no funciona")
        
        browser.close()

if __name__ == "__main__":
    final_verification()