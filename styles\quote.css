/* Estilos para el canvas de cotización */
.quote-canvas {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%; /* Or a specific height for your canvas */
    overflow: hidden;
}

.canvas-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.canvas-overlay.active {
    display: block;
}

.quote-canvas {
    position: fixed;
    top: 0;
    right: -800px;
    width: 800px;
    height: 100vh;
    background-color: #fff;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    transition: right 0.3s ease-in-out;
    overflow: hidden; /* Evitar scroll en el canvas principal */
    padding: 0; /* Remover padding del canvas principal */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.quote-canvas.active {
    right: 0;
}

/* Notificación de producto agregado */
.quote-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 10000;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.quote-notification.show {
    opacity: 1;
    transform: translateY(0);
}

.quote-notification i {
    font-size: 1.2rem;
}

.quote-notification span {
    font-weight: 500;
}

/* Contenedor deslizante para múltiples niveles */
.quote-slide-container {
    position: relative;
    flex: 1;
    overflow: hidden;
}

.quote-slide-inner {
    display: flex;
    transition: transform 0.3s ease;
    height: 100%;
}

.quote-level {
    width: 100%;
    flex-shrink: 0;
    overflow-y: auto;
    padding: 20px;
}

/* Transiciones entre niveles */
.quote-slide-inner.show-list {
    transform: translateX(-100%);
}

.quote-slide-inner.show-detail {
    transform: translateX(-200%);
}

.quote-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e1e1;
}

.quote-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.close-canvas {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    transition: color 0.3s;
}

.close-canvas:hover {
    color: #e74c3c;
}

.quote-form-section {
    margin-bottom: 25px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quote-form-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 1.2rem;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 8px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 15px;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.modern-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.modern-input:focus {
    border-color: #4e73df;
    outline: none;
    box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);
}

.tabs-container {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid #e1e1e1;
}

.tab-button {
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    color: #555;
    font-weight: 500;
    transition: all 0.3s;
}

.tab-button.active {
    color: #4e73df;
    border-bottom-color: #4e73df;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.search-results {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 10px;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
}

.search-result-item {
    padding: 10px;
    border-bottom: 1px solid #f1f1f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background-color: #f8f9fc;
}

.search-result-item:last-child {
    border-bottom: none;
}

.quote-items-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
}

.empty-state {
    padding: 30px;
    text-align: center;
    color: #999;
}

.empty-state i {
    font-size: 40px;
    margin-bottom: 10px;
    display: block;
}

.quote-item {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #f1f1f1;
    align-items: center;
}

.quote-item-info {
    flex: 1;
}

.quote-item-name {
    font-weight: 500;
    margin-bottom: 3px;
}

.quote-item-description {
    font-size: 12px;
    color: #777;
    margin-bottom: 3px;
}

.quote-item-price {
    font-size: 14px;
    color: #2c3e50;
}

.quote-item-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quote-item-quantity {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quantity-btn {
    width: 25px;
    height: 25px;
    border: none;
    background: #4e73df;
    color: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s;
}

.quantity-btn:hover {
    background: #4e73df;
    color: white;
    border-color: #4e73df;
}

.remove-item-btn {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.2s;
}

.remove-item-btn:hover {
    color: #c0392b;
}

.quote-totals {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fc;
    border-radius: 4px;
    border: 1px solid #e1e1e1;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.total-row.total-final {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e1e1e1;
    font-weight: bold;
    font-size: 16px;
    color: #2c3e50;
}

.quote-content {
    flex-grow: 1;
    overflow-y: auto;
}

/* Botones fijos al final (deshabilitado) */
.quote-actions {
    display: none;
}

/* Nuevo contenedor de botones en la parte superior */
.quote-actions-top {
    width: 100%;
    background-color: #f8f9fc; 
    padding: 15px 20px;
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin-bottom: 20px;
    border-radius: 8px;
    flex-shrink: 0; /* No se reduce el tamaño de los botones */
}

.action-button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    color: white;
    background-color: #007bff; /* Default blue color */
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
}

.action-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.action-button.secondary {
    background-color: #6c757d;
}

.action-button.save {
    background-color: #28a745;
}

.action-button.primary {
    background-color: #007bff;
}

.action-button.info {
    background-color: #17a2b8;
}

.quote-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 15px 20px;
    background-color: #28a745;
    color: white;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Estilos específicos para el contador de cotizaciones */
.quote-content {
    padding-bottom: 20px; /* Reduced since we don't have buttons at the bottom anymore */
}

/* Estilos para el sistema de navegación deslizante */
.quote-slide-container {
    position: relative;
    width: 100%;
    height: 100%; /* Ocupar todo el espacio disponible después del header */
    flex: 1; /* Expandir para llenar el espacio restante */
    overflow: hidden;
}

.quote-slide-inner {
    display: flex;
    width: 300%; /* 3 niveles x 100% cada uno */
    height: 100%;
    transition: transform 0.4s ease-in-out;
}

.quote-level {
    width: 33.333%; /* 100% / 3 niveles */
    height: 100%;
    overflow-y: auto;
    padding: 0;
}

/* Header con botón de volver */
.quote-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #e1e1e1;
    background-color: #fff;
    z-index: 10;
    flex-shrink: 0; /* No se reduce el tamaño del header */
    min-height: 60px; /* Altura mínima fija */
}

.back-btn {
    background: none;
    border: none;
    font-size: 1rem;
    color: #666;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s;
}

.back-btn:hover {
    background-color: #f0f0f0;
    color: #333;
}

/* Estilos para la lista de cotizaciones */
.quote-list-content {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.quote-search-bar {
    margin-bottom: 20px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-wrapper i {
    position: absolute;
    left: 15px;
    color: #999;
}

.search-input-wrapper input {
    width: 100%;
    padding: 10px 10px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.quotes-list-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.quote-list-item {
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 15px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s;
}

.quote-list-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.quote-info h4 {
    margin: 0 0 5px 0;
    color: #333;
}

.quote-info p {
    margin: 0;
    font-size: 0.9rem;
    color: #666;
}

.quote-amount {
    text-align: right;
}

.quote-amount .total {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4e73df;
}

.quote-amount .items-count {
    font-size: 0.85rem;
    color: #999;
}

.quote-actions {
    display: flex;
    gap: 5px;
}

.action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #f0f0f0;
    color: #333;
}

.action-btn:hover {
    background-color: #e0e0e0;
}

.action-btn.view {
    color: #4e73df;
}

.action-btn.pdf {
    color: #dc3545;
}

.action-btn.delete {
    color: #e74c3c;
}

/* Estados vacíos y de carga */
.loading-quotes, .no-quotes, .error-message {
    text-align: center;
    padding: 40px;
    color: #999;
}

.loading-quotes i, .no-quotes i {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

/* Estilos para items de cotización */
.quote-items-container {
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    min-height: 200px;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #999;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 10px;
    display: block;
}

.quote-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.quote-item:last-child {
    border-bottom: none;
}

.item-info h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
}

.item-info .item-price {
    color: #666;
    font-size: 0.9rem;
}

.item-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.quantity-btn:hover {
    background: #4e73df;
    color: white;
}

.item-total {
    font-weight: bold;
    color: #333;
    min-width: 100px;
    text-align: right;
}

.remove-btn {
    background: none;
    border: none;
    color: #e74c3c;
    cursor: pointer;
    padding: 5px 10px;
}

.remove-btn:hover {
    background: #fee;
    border-radius: 4px;
}

/* Totales */
.quote-totals {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fc;
    border-radius: 4px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
}

.total-final {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4e73df;
    border-top: 2px solid #e1e1e1;
    padding-top: 10px;
    margin-top: 10px;
}

/* Estilos para botones de acción superiores */
.quote-actions-top {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding: 0 20px;

/* Ajustes para el contenedor del formulario */
#quoteFormLevel {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
}

#quoteFormLevel .quote-content {
    padding: 20px;
    flex: 1; /* Expandir para llenar el espacio restante */
    overflow-y: auto;
    box-sizing: border-box;
}

/* Estilos para la lista de cotizaciones */
.quote-list-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.quote-search-bar {
    padding: 20px;
    background-color: #f8f9fc;
    border-bottom: 1px solid #e1e1e1;
}

.search-input-wrapper {
    position: relative;
}

.search-input-wrapper i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.quote-search-bar .modern-input {
    padding-left: 40px;
    width: 100%;
}

.quotes-list-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* Estilos para las tarjetas de cotización */
.quote-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.quote-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #4e73df;
}

.quote-header-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: #666;
}

.quote-client {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.quote-total {
    font-size: 1.1rem;
    color: #4e73df;
    font-weight: bold;
}

/* Estilos para el detalle de cotización */
.quote-detail-content {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

.quote-detail {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.quote-detail h3 {
    color: #2c3e50;
    margin-top: 20px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e1e1;
}

.quote-detail p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.quote-detail strong {
    color: #555;
    margin-right: 10px;
}

.quote-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.quote-items li {
    padding: 10px;
    border-bottom: 1px solid #f1f1f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.quote-items li:last-child {
    border-bottom: none;
}

/* Estados de posición para el contenedor deslizante */
.quote-slide-inner.show-list {
    transform: translateX(-33.333%);
}

.quote-slide-inner.show-detail {
    transform: translateX(-66.666%);
}

.quote-count {
    display: inline-block;
    background-color: #3498db;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    position: absolute;
    top: -5px;
    right: -5px;
    transition: all 0.3s ease;
    /* Ocultar el contador si es 0 */
    visibility: hidden; /* Oculto por defecto */
}

/* Animación para el contador cuando se actualiza */
.quote-count.updated {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
    100% {
        transform: scale(1);
    }
}

/* Estilos para el botón de cotización */
#openQuoteBtn {
    position: relative;
    margin: 0 10px;
}

/* Estilos para el icono del botón de cotización */
#openQuoteBtn i {
    font-size: 18px;
}