<?php
require_once 'utils.php';
// Habilitar logging detallado
write_log("===== INICIO: SOLICITUD DE FOLIOS CAF (PHP) =====");

// Función para logging
function log_message($message, $level = 'INFO') {
    write_log("[$level] $message");
}

// Configuración de cabeceras para la respuesta
header('Content-Type: application/json');
log_message("Cabeceras configuradas para JSON");

// Obtener los datos de la solicitud
log_message("Intentando leer datos de la solicitud...");
$raw_input = file_get_contents('php://input');
log_message("Datos brutos recibidos: " . substr($raw_input, 0, 100) . "...");

$requestData = json_decode($raw_input, true);

if (!$requestData) {
    log_message("Error al decodificar JSON de la solicitud", "ERROR");
    echo json_encode(['success' => false, 'error' => 'Datos de solicitud inválidos']);
    write_log("===== FIN: SOLICITUD DE FOLIOS CAF (ERROR) =====");
    exit;
}

log_message("Datos JSON decodificados correctamente");

// Extraer los datos necesarios
$rutCertificado = $requestData['rutCertificado'] ?? '17365958-K';
$password = $requestData['password'] ?? '1569';
$rutEmpresa = $requestData['rutEmpresa'] ?? '78078979-4';
$ambiente = $requestData['ambiente'] ?? 1;
$tipoDTE = $requestData['tipoDTE'] ?? '33';
$cantidad = $requestData['cantidad'] ?? 19;
$certificadoPath = $requestData['certificadoPath'] ?? 'Documents/17365958-K.pfx';
$folderDestino = $requestData['folderDestino'] ?? 'Documents/folios/Facturas';
$fileName = $requestData['fileName'] ?? 'folios_' . $tipoDTE . '_' . time() . '.xml';

log_message("Parámetros extraídos:
    - RUT Certificado: $rutCertificado
    - RUT Empresa: $rutEmpresa
    - Ambiente: $ambiente
    - Tipo DTE: $tipoDTE
    - Cantidad: $cantidad
    - Certificado Path: $certificadoPath
    - Folder Destino: $folderDestino
    - Nombre Archivo: $fileName");

// Verificar si el archivo de certificado existe
log_message("Verificando si existe el archivo de certificado: $certificadoPath");
if (!file_exists($certificadoPath)) {
    log_message("El archivo de certificado no existe: $certificadoPath", "ERROR");
    echo json_encode([
        'success' => false,
        'error' => 'El archivo de certificado no existe',
        'details' => 'Ruta: ' . $certificadoPath
    ]);
    write_log("===== FIN: SOLICITUD DE FOLIOS CAF (ERROR) =====");
    exit;
}
log_message("Archivo de certificado encontrado");

// Asegurarse de que el directorio de destino exista
log_message("Verificando si existe el directorio destino: $folderDestino");
if (!is_dir($folderDestino)) {
    log_message("El directorio destino no existe, intentando crearlo...");
    if (!mkdir($folderDestino, 0777, true)) {
        log_message("No se pudo crear el directorio destino: $folderDestino", "ERROR");
        echo json_encode([
            'success' => false,
            'error' => 'No se pudo crear el directorio de destino',
            'details' => 'Ruta: ' . $folderDestino
        ]);
        write_log("===== FIN: SOLICITUD DE FOLIOS CAF (ERROR) =====");
        exit;
    }
    log_message("Directorio destino creado con éxito");
} else {
    log_message("Directorio destino ya existe");
}

// Preparar el JSON de entrada exactamente igual que en el ejemplo
$inputJson = json_encode([
    "RutCertificado" => $rutCertificado,
    "Password" => $password,
    "RutEmpresa" => $rutEmpresa,
    "Ambiente" => $ambiente
]);
log_message("JSON input preparado: $inputJson");

// URL de la API
$url = "https://servicios.simpleapi.cl/api/folios/get/{$tipoDTE}/{$cantidad}";
log_message("URL de la API: $url");

// Inicializar cURL
log_message("Inicializando cURL");
$curl = curl_init();

// Preparar el formulario multipart de manera similar al ejemplo de jQuery
$boundary = uniqid();
$delimiter = '-------------' . $boundary;

// Leer el archivo del certificado
$fileContents = file_get_contents($certificadoPath);
$fileName = basename($certificadoPath);

// Construir el cuerpo de la petición multipart
$data = "--" . $delimiter . "\r\n";
$data .= 'Content-Disposition: form-data; name="input"' . "\r\n\r\n";
$data .= $inputJson . "\r\n";
$data .= "--" . $delimiter . "\r\n";
$data .= 'Content-Disposition: form-data; name="files"; filename="' . $fileName . '"' . "\r\n";
$data .= 'Content-Type: application/octet-stream' . "\r\n\r\n";
$data .= $fileContents . "\r\n";
$data .= "--" . $delimiter . "--\r\n";

log_message("Cuerpo multipart preparado con boundary: $boundary");

// Configurar las opciones de cURL
log_message("Configurando opciones de cURL");
curl_setopt_array($curl, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => $data,
    CURLOPT_HTTPHEADER => [
        "Authorization: 2037-N680-6391-2493-5987",
        "Content-Type: multipart/form-data; boundary=" . $delimiter,
        "Content-Length: " . strlen($data)
    ],
]);

log_message("Headers configurados correctamente con multipart/form-data");

// Ejecutar la solicitud
log_message("Ejecutando solicitud cURL a Simple API...");
$response = curl_exec($curl);
$err = curl_error($curl);
$info = curl_getinfo($curl);

log_message("Info de la solicitud:
    - HTTP Code: {$info['http_code']}
    - Total Time: {$info['total_time']}
    - Size: {$info['size_download']} bytes");

// Cerrar la sesión cURL
curl_close($curl);
log_message("Sesión cURL cerrada");

// Verificar si hubo un error en la solicitud
if ($err) {
    log_message("Error en la solicitud cURL: $err", "ERROR");
    echo json_encode([
        'success' => false,
        'error' => 'Error al realizar la solicitud cURL',
        'details' => $err
    ]);
    write_log("===== FIN: SOLICITUD DE FOLIOS CAF (ERROR) =====");
    exit;
}

// Verificar el tamaño y contenido de la respuesta
$responseSize = strlen($response);
log_message("Respuesta recibida. Tamaño: $responseSize bytes");

if ($responseSize === 0) {
    log_message("La respuesta está vacía", "ERROR");
    echo json_encode([
        'success' => false,
        'error' => 'La respuesta del servidor está vacía'
    ]);
    write_log("===== FIN: SOLICITUD DE FOLIOS CAF (ERROR) =====");
    exit;
}

// Guardar los primeros 100 bytes de la respuesta para diagnóstico
$responseSample = substr($response, 0, 100);
log_message("Muestra de respuesta: $responseSample");

// Guardar el archivo PFX en la ruta especificada
log_message("Intentando guardar el archivo en: $folderDestino/$fileName");
$filePath = $folderDestino . '/' . $fileName;
$bytesWritten = file_put_contents($filePath, $response);

if ($bytesWritten === false) {
    log_message("Error al guardar el archivo", "ERROR");
    echo json_encode([
        'success' => false,
        'error' => 'Error al guardar el archivo',
        'details' => 'Ruta: ' . $filePath
    ]);
    write_log("===== FIN: SOLICITUD DE FOLIOS CAF (ERROR) =====");
    exit;
}
log_message("Archivo guardado correctamente. Bytes escritos: $bytesWritten");

// Información básica del archivo
$fileInfo = [
    'size' => $bytesWritten,
    'path' => $filePath,
    'created' => date('Y-m-d H:i:s')
];

// Devolver una respuesta exitosa
log_message("Enviando respuesta exitosa al cliente");
$response = [
    'success' => true,
    'message' => 'Archivo CAF guardado correctamente',
    'fileName' => $fileName,
    'filePath' => $filePath,
    'fileInfo' => $fileInfo
];

echo json_encode($response);
log_message("Respuesta JSON enviada: " . json_encode($response));

write_log("===== FIN: SOLICITUD DE FOLIOS CAF (ÉXITO) =====");
?>