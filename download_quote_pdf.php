<?php
session_start();

// Intentar cargar TCPDF desde diferentes ubicaciones
$tcpdf_loaded = false;

// Opción 1: Composer
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    $tcpdf_loaded = true;
}
// Opción 2: Instalación manual en directorio tcpdf
elseif (file_exists('tcpdf/tcpdf.php')) {
    require_once 'tcpdf/tcpdf.php';
    $tcpdf_loaded = true;
}
// Opción 3: Instalación manual en directorio lib
elseif (file_exists('lib/tcpdf/tcpdf.php')) {
    require_once 'lib/tcpdf/tcpdf.php';
    $tcpdf_loaded = true;
}

// Si no se pudo cargar TCPDF, mostrar error informativo
if (!$tcpdf_loaded) {
    header('HTTP/1.0 500 Internal Server Error');
    die('Error: TCPDF no está instalado. Por favor, instale TCPDF para generar PDFs.');
}

// Configurar zona horaria para Santiago de Chile
date_default_timezone_set('America/Santiago');

// Verificar autenticación
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('HTTP/1.0 401 Unauthorized');
    die('No autorizado');
}

// Obtener ID de la cotización
$quoteId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($quoteId <= 0) {
    header('HTTP/1.0 400 Bad Request');
    die('ID de cotización inválido');
}

// Conectar a la base de datos y obtener datos de la cotización
require_once 'db_connection.php';

try {
    $conn = getConnection();
    
    // Obtener datos principales de la cotización
    $stmt = $conn->prepare("SELECT * FROM tb_cotizaciones WHERE id = :id");
    $stmt->execute(['id' => $quoteId]);
    $quote = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$quote) {
        header('HTTP/1.0 404 Not Found');
        die('Cotización no encontrada');
    }
    
    // Obtener items de la cotización
    $stmtItems = $conn->prepare("SELECT * FROM tb_cotizacion_items WHERE cotizacion_id = :id");
    $stmtItems->execute(['id' => $quoteId]);
    $items = $stmtItems->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    header('HTTP/1.0 500 Internal Server Error');
    die('Error al obtener datos de la cotización: ' . $e->getMessage());
}

// Clase personalizada para PDF térmico
class ThermalPDF extends TCPDF {
    public function Header() {
        // Logo o encabezado de la empresa
        $this->SetFont('helvetica', 'B', 12);
        $this->Cell(0, 10, 'TATA REPUESTOS', 0, 1, 'C');
        $this->SetFont('helvetica', '', 8);
        $this->Cell(0, 5, 'Repuestos Peugeot, Citroën y Renault', 0, 1, 'C');
        $this->Cell(0, 5, 'Temuco, Chile', 0, 1, 'C');
        $this->Ln(5);
    }
    
    public function Footer() {
        $this->SetY(-15);
        $this->SetFont('helvetica', 'I', 6);
        $this->Cell(0, 5, 'Página ' . $this->getAliasNumPage() . '/' . $this->getAliasNbPages(), 0, 0, 'C');
    }
}

// Crear instancia del PDF
// 80mm = 80 * 0.0393701 = 3.15 pulgadas
$pdf = new ThermalPDF('P', 'mm', array(80, 297), true, 'UTF-8', false);

// Configuración del documento
$pdf->SetCreator('TATA REPUESTOS');
$pdf->SetAuthor('TATA REPUESTOS');
$pdf->SetTitle('Cotización #' . $quote['numero']);
$pdf->SetSubject('Cotización de Productos');

// Configurar márgenes para impresora térmica
$pdf->SetMargins(5, 5, 5);
$pdf->SetHeaderMargin(5);
$pdf->SetFooterMargin(5);

// Configurar saltos de página automáticos
$pdf->SetAutoPageBreak(TRUE, 25);

// Configurar factor de escala de imagen
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

// Agregar página
$pdf->AddPage();

// Título del documento
$pdf->SetFont('helvetica', 'B', 10);
$pdf->Cell(0, 8, 'COTIZACIÓN', 0, 1, 'C');
$pdf->Ln(2);

// Información de la cotización
$pdf->SetFont('helvetica', 'B', 8);
$pdf->Cell(0, 5, 'Número: ' . $quote['numero'], 0, 1, 'L');
$pdf->Cell(0, 5, 'Fecha: ' . date('d/m/Y H:i', strtotime($quote['fecha'])), 0, 1, 'L');
$pdf->Ln(3);

// Información del cliente
$pdf->SetFont('helvetica', 'B', 8);
$pdf->Cell(0, 5, 'DATOS DEL CLIENTE', 0, 1, 'L');
$pdf->SetFont('helvetica', '', 7);

if (!empty($quote['cliente_nombre'])) {
    $pdf->Cell(0, 4, 'Nombre: ' . $quote['cliente_nombre'], 0, 1, 'L');
}
if (!empty($quote['cliente_rut'])) {
    $pdf->Cell(0, 4, 'RUT: ' . $quote['cliente_rut'], 0, 1, 'L');
}
if (!empty($quote['cliente_email'])) {
    $pdf->Cell(0, 4, 'Email: ' . $quote['cliente_email'], 0, 1, 'L');
}
if (!empty($quote['cliente_telefono'])) {
    $pdf->Cell(0, 4, 'Teléfono: ' . $quote['cliente_telefono'], 0, 1, 'L');
}

$pdf->Ln(5);

// Productos
$pdf->SetFont('helvetica', 'B', 8);
$pdf->Cell(0, 5, 'PRODUCTOS', 0, 1, 'L');
$pdf->Ln(2);

// Encabezados de tabla
$pdf->SetFont('helvetica', 'B', 6);
$pdf->Cell(35, 4, 'Producto', 1, 0, 'C');
$pdf->Cell(10, 4, 'Cant.', 1, 0, 'C');
$pdf->Cell(12, 4, 'Precio', 1, 0, 'C');
$pdf->Cell(13, 4, 'Total', 1, 1, 'C');

// Datos de productos
$pdf->SetFont('helvetica', '', 6);
$total = 0;

foreach ($items as $item) {
    $totalItem = $item['precio_unitario'] * $item['cantidad'];
    $total += $totalItem;
    
    // Nombre del producto (puede ser largo, usar MultiCell si es necesario)
    $pdf->Cell(35, 4, substr($item['nombre'], 0, 25), 1, 0, 'L');
    $pdf->Cell(10, 4, $item['cantidad'], 1, 0, 'C');
    $pdf->Cell(12, 4, '$' . number_format($item['precio_unitario'], 0, ',', '.'), 1, 0, 'R');
    $pdf->Cell(13, 4, '$' . number_format($totalItem, 0, ',', '.'), 1, 1, 'R');
}

$pdf->Ln(3);

// Total
$pdf->SetFont('helvetica', 'B', 8);
$pdf->Cell(57, 6, 'TOTAL:', 1, 0, 'R');
$pdf->Cell(13, 6, '$' . number_format($total, 0, ',', '.'), 1, 1, 'R');

$pdf->Ln(5);

// Notas adicionales
if (!empty($quote['notas'])) {
    $pdf->SetFont('helvetica', 'B', 7);
    $pdf->Cell(0, 4, 'NOTAS:', 0, 1, 'L');
    $pdf->SetFont('helvetica', '', 6);
    $pdf->MultiCell(0, 3, $quote['notas'], 0, 'L');
    $pdf->Ln(3);
}

// Información adicional
$pdf->SetFont('helvetica', 'I', 6);
$pdf->Cell(0, 3, 'Esta cotización tiene validez de 30 días.', 0, 1, 'C');
$pdf->Cell(0, 3, 'Precios sujetos a cambio sin previo aviso.', 0, 1, 'C');

// Generar el PDF
$pdfContent = $pdf->Output('', 'S');

// Enviar headers para descarga
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="Cotizacion_' . $quote['numero'] . '.pdf"');
header('Content-Length: ' . strlen($pdfContent));

echo $pdfContent;
?>
