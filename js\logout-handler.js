// Manejador de cierre de sesión
document.addEventListener('DOMContentLoaded', function() {
    // Buscar todos los enlaces de logout
    const logoutLinks = document.querySelectorAll('a[href="logout.php"]');
    
    logoutLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Limpiar cualquier almacenamiento local
            if (typeof(Storage) !== "undefined") {
                localStorage.clear();
                sessionStorage.clear();
            }
            
            // Deshabilitar el caché del navegador
            const meta = document.createElement('meta');
            meta.httpEquiv = 'Cache-Control';
            meta.content = 'no-cache, no-store, must-revalidate';
            document.getElementsByTagName('head')[0].appendChild(meta);
            
            // Redirigir a logout.php con timestamp para evitar caché
            window.location.href = 'logout.php?t=' + Date.now();
            
            // Forzar recarga después de un breve delay
            setTimeout(() => {
                window.location.reload(true);
            }, 100);
        });
    });
});

// Función para verificar el estado de la sesión
function checkSessionStatus() {
    fetch('check_session.php')
        .then(response => response.json())
        .then(data => {
            if (!data.logged_in) {
                window.location.href = 'login.php';
            }
        })
        .catch(error => {
            console.error('Error checking session:', error);
        });
}

// Verificar el estado de la sesión cada 5 minutos
setInterval(checkSessionStatus, 300000);