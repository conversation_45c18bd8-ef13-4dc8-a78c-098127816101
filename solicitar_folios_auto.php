<?php
/**
 * Script para solicitar folios CAF automáticamente cuando se alcanza el límite
 * Este script puede ser llamado desde JavaScript cuando se detecta que se ha alcanzado el límite de folios
 */

// Configurar reporte de errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir archivo de conexión a la base de datos
require_once 'db_connection.php';

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Obtener el tipo de documento del POST
$tipoDTE = $_POST['tipoDTE'] ?? null;

if (!$tipoDTE) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Tipo de documento no especificado']);
    exit;
}

// Registrar la solicitud en el log
write_log("=== SOLICITUD AUTOMÁTICA DE FOLIOS CAF ===");
write_log("Tipo DTE: $tipoDTE");

// Determinar la cantidad de folios según el tipo de documento
$cantidad = 19; // Valor por defecto para facturas
switch ($tipoDTE) {
    case '33': // Facturas
        $cantidad = 19;
        $folderName = 'Facturas';
        break;
    case '39': // Boletas
        $cantidad = 500;
        $folderName = 'Boletas';
        break;
    case '61': // Notas de Crédito
        $cantidad = 2;
        $folderName = 'NotasCredito';
        break;
    case '56': // Notas de Débito
        $cantidad = 1;
        $folderName = 'NotasDebito';
        break;
    default:
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Tipo de documento no válido']);
        exit;
}

write_log("Cantidad de folios a solicitar: $cantidad");
write_log("Carpeta destino: $folderName");

// Configuración
$apiKey = '2037-N680-6391-2493-5987';
$rutCertificado = '17365958-K';
$password = '1569';
$rutEmpresa = '78078979-4';
$ambiente = 1; // 1: Certificación, 0: Producción

// Ruta al certificado
$certificadoPath = 'Documents/17365958-K.pfx';
if (!file_exists($certificadoPath)) {
    write_log("ERROR: Archivo de certificado no encontrado: $certificadoPath");
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Archivo de certificado no encontrado: ' . $certificadoPath]);
    exit;
}

// Crear directorio de destino si no existe
$foliosDir = "Documents/folios/$folderName";
if (!is_dir($foliosDir)) {
    if (!mkdir($foliosDir, 0755, true)) {
        write_log("ERROR: No se pudo crear el directorio para guardar los folios: $foliosDir");
        header('Content-Type: application/json');
        echo json_encode(['error' => 'No se pudo crear el directorio para guardar los folios']);
        exit;
    }
}

// Preparar datos para la solicitud
$input = json_encode([
    'RutCertificado' => $rutCertificado,
    'Password' => $password,
    'RutEmpresa' => $rutEmpresa,
    'Ambiente' => $ambiente
]);

// Crear el nombre del archivo de destino
$fechaActual = date('Ymd_His');
$nombreArchivo = "folios_{$tipoDTE}_{$fechaActual}.xml";
$rutaCompleta = "{$foliosDir}/{$nombreArchivo}";

write_log("Nombre de archivo: $nombreArchivo");
write_log("Ruta completa: $rutaCompleta");

// Inicializar cURL
$curl = curl_init();

// Preparar el archivo para la solicitud
$cfile = new CURLFile($certificadoPath, 'application/x-pkcs12', basename($certificadoPath));

// URL de la API
$apiUrl = "https://servicios.simpleapi.cl/api/folios/get/{$tipoDTE}/{$cantidad}";
write_log("URL de la API: $apiUrl");

// Configurar cURL
curl_setopt_array($curl, [
    CURLOPT_URL => $apiUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => [
        'input' => $input,
        'files' => $cfile
    ],
    CURLOPT_HTTPHEADER => [
        'Authorization: ' . $apiKey
    ],
    CURLOPT_VERBOSE => true
]);

// Capturar información detallada de cURL
$verbose = fopen('php://temp', 'w+');
curl_setopt($curl, CURLOPT_STDERR, $verbose);

// Registrar inicio de la solicitud
$tiempoInicio = microtime(true);
write_log("Iniciando solicitud cURL...");

// Ejecutar la solicitud
$response = curl_exec($curl);
$err = curl_error($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$tiempoTotal = round((microtime(true) - $tiempoInicio) * 1000); // Tiempo en milisegundos

// Obtener información detallada de debug
rewind($verbose);
$verboseLog = stream_get_contents($verbose);
fclose($verbose);
write_log("Debug cURL: " . $verboseLog);

curl_close($curl);

write_log("Tiempo de respuesta: $tiempoTotal ms");
write_log("Código HTTP: $httpCode");

// Verificar si hubo errores
if ($err) {
    write_log("ERROR cURL: $err");
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Error en la solicitud cURL: ' . $err]);
    exit;
}

// Verificar el código de respuesta HTTP
if ($httpCode != 200) {
    write_log("ERROR HTTP: $httpCode");
    write_log("Respuesta: $response");
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'Error en la respuesta del servidor: Código ' . $httpCode,
        'response' => $response
    ]);
    exit;
}

// Guardar la respuesta en un archivo XML
if (file_put_contents($rutaCompleta, $response) === false) {
    write_log("ERROR: No se pudo guardar el archivo XML en $rutaCompleta");
    header('Content-Type: application/json');
    echo json_encode(['error' => 'No se pudo guardar el archivo XML']);
    exit;
}

write_log("Archivo XML guardado en: $rutaCompleta");

// Extraer el rango de folios del XML
$xml = simplexml_load_string($response);
if ($xml === false) {
    write_log("ERROR: No se pudo analizar la respuesta XML");
    header('Content-Type: application/json');
    echo json_encode(['error' => 'No se pudo analizar la respuesta XML']);
    exit;
}

// Extraer los valores de rango inicial y final
$rangoInicial = null;
$rangoFinal = null;

if (isset($xml->CAF->DA->RNG->D)) {
    $rangoInicial = (int)$xml->CAF->DA->RNG->D;
}

if (isset($xml->CAF->DA->RNG->H)) {
    $rangoFinal = (int)$xml->CAF->DA->RNG->H;
}

if ($rangoInicial === null || $rangoFinal === null) {
    write_log("ERROR: No se pudieron extraer los rangos de folios del XML");
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'No se pudieron extraer los rangos de folios del XML',
        'xml_content' => $response
    ]);
    exit;
}

write_log("Rango inicial: $rangoInicial");
write_log("Rango final: $rangoFinal");
write_log("Total folios: " . ($rangoFinal - $rangoInicial + 1));

// Registrar en la base de datos
try {
    $conn = getConnection();

    $stmt = $conn->prepare("
        INSERT INTO folios_caf (
            tipo_documento,
            rango_inicial,
            rango_final,
            siguiente_folio,
            ruta_archivo,
            activo,
            created_at
        ) VALUES (
            :tipo_documento,
            :rango_inicial,
            :rango_final,
            :siguiente_folio,
            :ruta_archivo,
            1,
            NOW()
        )
    ");

    $stmt->bindParam(':tipo_documento', $tipoDTE);
    $stmt->bindParam(':rango_inicial', $rangoInicial);
    $stmt->bindParam(':rango_final', $rangoFinal);
    $stmt->bindParam(':siguiente_folio', $rangoInicial); // siguiente_folio = rango_inicial
    $stmt->bindParam(':ruta_archivo', $rutaCompleta);

    $stmt->execute();
    $folioId = $conn->lastInsertId();

    write_log("Folios registrados en la base de datos. ID: $folioId");

    // Preparar logs detallados para el cliente
    $logs = [
        [
            'type' => 'info',
            'message' => 'Conexión con SimpleAPI establecida',
            'data' => [
                'tiempo_respuesta' => $tiempoTotal . ' ms',
                'codigo_http' => $httpCode
            ]
        ],
        [
            'type' => 'info',
            'message' => 'Solicitud de folios procesada por el servidor',
            'data' => [
                'tipo_dte' => $tipoDTE,
                'cantidad' => $cantidad,
                'ambiente' => $ambiente == 1 ? 'Certificación' : 'Producción',
                'fecha_hora' => date('Y-m-d H:i:s')
            ]
        ],
        [
            'type' => 'success',
            'message' => 'Archivo XML recibido y guardado',
            'data' => [
                'ruta' => $rutaCompleta,
                'nombre_archivo' => $nombreArchivo,
                'tamano' => filesize($rutaCompleta) . ' bytes'
            ]
        ],
        [
            'type' => 'success',
            'message' => 'Rango de folios obtenido',
            'data' => [
                'rango_inicial' => $rangoInicial,
                'rango_final' => $rangoFinal,
                'total_folios' => ($rangoFinal - $rangoInicial + 1)
            ]
        ],
        [
            'type' => 'success',
            'message' => 'Registro en base de datos completado',
            'data' => [
                'folio_id' => $folioId,
                'tipo_documento' => $tipoDTE,
                'tabla' => 'folios_caf'
            ]
        ]
    ];

    // Devolver respuesta exitosa con logs detallados
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'Folios solicitados y registrados correctamente',
        'folio_id' => $folioId,
        'rango_inicial' => $rangoInicial,
        'rango_final' => $rangoFinal,
        'ruta_archivo' => $rutaCompleta,
        'archivo_xml' => $nombreArchivo,
        'logs' => $logs
    ]);

} catch (PDOException $e) {
    write_log("ERROR PDO: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Error al registrar en la base de datos: ' . $e->getMessage()]);
    exit;
}
?>
