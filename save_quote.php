<?php
require_once 'auth_check.php';
require_once 'db_connection.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'Datos inválidos']);
    exit;
}

try {
    $conn = getConnection();
    $conn->beginTransaction();
    
    // Generar número de cotización
    $year = date('Y');
    $month = date('m');
    $prefix = "COT-{$year}{$month}-";
    
    // Obtener el último número
    $sql = "SELECT MAX(CAST(SUBSTRING(numero, LENGTH(?) + 1) AS UNSIGNED)) as ultimo_numero
            FROM tb_cotizaciones 
            WHERE numero LIKE ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$prefix, $prefix . '%']);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $siguiente_numero = ($result['ultimo_numero'] ?? 0) + 1;
    $numero_cotizacion = $prefix . str_pad($siguiente_numero, 4, '0', STR_PAD_LEFT);
    
    // Calcular totales
    $subtotal = $input['total'] ?? 0;
    $iva = $subtotal * 0.19;
    $total = $subtotal + $iva;
    
    // Insertar cotización
    $sql = "INSERT INTO tb_cotizaciones (
                numero,
                cliente_nombre,
                cliente_rut,
                cliente_email,
                cliente_telefono,
                subtotal,
                iva,
                total,
                notas,
                usuario_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        $numero_cotizacion,
        $input['client']['name'] ?? '',
        $input['client']['rut'] ?? '',
        $input['client']['email'] ?? '',
        $input['client']['phone'] ?? '',
        $subtotal,
        $iva,
        $total,
        $input['notes'] ?? '',
        $_SESSION['user_id'] ?? null
    ]);
    
    $cotizacion_id = $conn->lastInsertId();
    
    // Insertar items
    if (!empty($input['items'])) {
        $sql = "INSERT INTO tb_cotizacion_items (
                    cotizacion_id,
                    repuesto_id,
                    nombre,
                    precio_unitario,
                    cantidad,
                    total_item
                ) VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        
        foreach ($input['items'] as $item) {
            $totalItem = $item['price'] * $item['quantity'];
            $repuestoId = !empty($item['id']) && !str_contains($item['id'], 'manual_') ? $item['id'] : null;
            
            $stmt->execute([
                $cotizacion_id,
                $repuestoId,
                $item['name'],
                $item['price'],
                $item['quantity'],
                $totalItem
            ]);
        }
    }
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Cotización guardada exitosamente',
        'cotizacion_id' => $cotizacion_id,
        'numero_cotizacion' => $numero_cotizacion
    ]);
    
} catch (Exception $e) {
    $conn->rollBack();
    echo json_encode([
        'success' => false,
        'message' => 'Error al guardar la cotización: ' . $e->getMessage()
    ]);
}
?>
