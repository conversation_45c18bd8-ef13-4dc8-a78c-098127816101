<?php
/**
 * Configuración de sesiones para prevenir cierre por inactividad
 * Incluir este archivo al inicio de cada script antes de session_start()
 */

// Establecer tiempo de vida de cookie de sesión a 30 días (en segundos)
ini_set('session.cookie_lifetime', 30 * 24 * 60 * 60); // 30 días

// Establecer tiempo máximo de sesión en el servidor a 30 días (en segundos)
ini_set('session.gc_maxlifetime', 30 * 24 * 60 * 60); // 30 días

// Prevenir que PHP transmita el ID de sesión como parte de la URL
ini_set('session.use_trans_sid', 0);

// Forzar el uso de cookies para la sesión
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);

// Asegurar que la cookie de sesión es segura
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_samesite', 'Strict');

// Establecer el nombre de la sesión para que sea más difícil de adivinar
session_name('TATA_REPUESTOS_SESSION');

// Establecer ruta para cookies de sesión
ini_set('session.cookie_path', '/');

// Deshabilitar cache limiter para ayudar con la persistencia de la sesión
session_cache_limiter('');

// Configurar el tiempo de vida del caché de la sesión a 30 días (en minutos)
session_cache_expire(30 * 24 * 60); // 30 días en minutos
?>
