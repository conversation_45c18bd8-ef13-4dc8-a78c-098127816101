<?php
require_once 'auth_check.php';
require_once 'db_connection.php';

header('Content-Type: application/json');

try {
    $conn = getConnection();
    
    // Consulta corregida para usar tb_cotizaciones
    $sql = "SELECT 
            c.id,
            c.numero as numero_cotizacion,
            c.cliente_nombre,
            c.cliente_rut,
            c.cliente_email,
            c.cliente_telefono,
            c.total,
            c.fecha as fecha_creacion,
            c.notas,
            COUNT(ci.id) as cantidad_items
        FROM tb_cotizaciones c
        LEFT JOIN tb_cotizacion_items ci ON c.id = ci.cotizacion_id
        GROUP BY c.id
        ORDER BY c.fecha DESC
        LIMIT 100";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    
    $quotes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'quotes' => $quotes
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener cotizaciones: ' . $e->getMessage()
    ]);
}
?>
