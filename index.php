<?php
// Incluir verificación de autenticación
require_once 'auth_check.php';

// Configuración adicional de caché
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Fecha en el pasado
?>

<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Agregar meta tags para control de caché -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>TATA REPUESTOS</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    
    <!-- Estilos críticos inline para evitar FOUC -->
    <style>
        /* Prevenir FOUC - Ocultar contenido hasta que los estilos estén cargados */
        body { 
            opacity: 0; 
            transition: opacity 0.3s ease-in-out;
        }
        body.loaded { 
            opacity: 1; 
        }
        
        /* Estilos básicos de layout para evitar saltos */
        header {
            height: 75px;
            background: #2c3e50;
        }
        .main-container {
            padding: 20px;
        }
        .products-grid {
            display: none;
        }
        .products-table {
            display: block;
        }

        html{
            zoom: 82%;
        }

        .products-table th[data-col="acciones"],
        .products-table td[data-col="acciones"] {
            width: 120px; /* Ancho para los botones */
            text-align: center;
            white-space: nowrap; /* Evitar que los botones se envuelvan */
        }
        .products-table td[data-col="acciones"] .button-group {
            display: ruby;
            gap: 5px; /* Espacio entre los botones */
            justify-content: center;
        }
        .products-table td[data-col="acciones"] .button-group button {
            width: 40px; /* Ancho fijo para todos los botones */
            height: 40px; /* Altura fija para todos los botones */
            padding: 8px; /* Padding uniforme */
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        /* Estilos para los botones en la vista de tarjetas */
        .product-card .button-group {
            display: ruby;
            gap: 5px; /* Espacio entre los botones */
            justify-content: center;
        }
        .product-card .button-group button {
            width: 40px; /* Ancho fijo para todos los botones */
            height: 40px; /* Altura fija para todos los botones */
            padding: 8px; /* Padding uniforme */
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex: none; /* Evitar que flex: 1 del CSS externo afecte el tamaño */
        }
    </style>
    
    <!-- Precargar CSS críticos -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">
    <link rel="preload" href="styles/header.css?v=<?php echo filemtime('styles/header.css'); ?>" as="style">
    <link rel="preload" href="styles/index.css?v=<?php echo filemtime('styles/index.css'); ?>" as="style">
    
    <!-- Cargar CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/table.css?v=<?php echo filemtime('styles/table.css'); ?>">
    <link rel="stylesheet" href="styles/cards.css?v=<?php echo filemtime('styles/cards.css'); ?>">
    <link rel="stylesheet" href="styles/controls.css?v=<?php echo filemtime('styles/controls.css'); ?>">
    <link rel="stylesheet" href="styles/header.css?v=<?php echo filemtime('styles/header.css'); ?>">
    <link rel="stylesheet" href="styles/index.css?v=<?php echo filemtime('styles/index.css'); ?>">
    <link rel="stylesheet" href="styles/search.css?v=<?php echo filemtime('styles/search.css'); ?>">
    <link rel="stylesheet" href="styles/search-fix.css?v=<?php echo filemtime('styles/search-fix.css'); ?>">
    <link rel="stylesheet" href="styles/dte.css?v=<?php echo filemtime('styles/dte.css'); ?>">
    <link rel="stylesheet" href="styles/folios.css?v=<?php echo filemtime('styles/folios.css'); ?>">
    <link rel="stylesheet" href="styles/quote.css?v=<?php echo filemtime('styles/quote.css'); ?>">
    <link rel="stylesheet" href="styles/quotes.css?v=<?php echo filemtime('styles/quotes.css'); ?>">

    <style>
        /* Estilos para controlar la visualización de las vistas */
        .products-grid {
            display: none; /* Oculto por defecto */
        }

        .products-table {
            display: block; /* Visible por defecto */
        }

        /* Cuando se activa la vista de tarjetas */
        body.grid-view .products-grid {
            display: grid !important;
        }

        body.grid-view .products-table {
            display: none !important;
        }

        /* Cuando se activa la vista de tabla */
        body.table-view .products-grid {
            display: none !important;
        }

        body.table-view .products-table {
            display: block !important;
        }

        /* Estilos para el contenedor de imagen y el icono de lupa */
        .product-image-container {
            position: relative;
            width: 100%;
            height: 150px;
            overflow: hidden;
        }

        .image-zoom-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .product-image-container:hover .image-zoom-icon {
            opacity: 1;
        }

        .icon-container > i,
        .icon-container > .icon-button { /* Aplicar espaciado también al botón */
            margin-left: 15px; /* Ajusta el espaciado si es necesario */
            cursor: pointer;
            font-size: 1.2rem; /* Ajusta el tamaño si es necesario */
            position: relative; /* Necesario para posicionar el contador */
        }

        /* NUEVO: Estilos para hacer que el botón parezca un icono */
        .icon-button {
            background: none;
            border: none;
            padding: 0; /* Quitar padding del botón */
            color: inherit; /* Heredar color del texto (o establece uno específico) */
            line-height: 1; /* Ajustar altura de línea */
            display: inline-flex; /* Alinear icono y contador si es necesario */
            align-items: center;
            justify-content: center;
        }

        /* Asegúrate de que el icono dentro del botón no tenga margen extra */
        .icon-button i {
            margin: 0;
        }

        /* Estilos para el contador DTE (mantener los de la respuesta anterior) */
        .dte-count, .quote-count {
            position: absolute;
            top: -10px; /* Sube un poco más */
            right: -10px; /* Mueve un poco más a la derecha */
            background-color: #e74c3c; /* Rojo */
            color: white;
            border-radius: 50%; /* Círculo perfecto */
            padding: 3px 7px; /* Aumenta el padding */
            font-size: 0.8rem; /* Aumenta ligeramente el tamaño de fuente */
            font-weight: bold;
            min-width: 20px; /* Asegura un tamaño mínimo */
            height: 20px; /* Asegura un tamaño mínimo */
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1; /* Asegura que el texto esté centrado verticalmente */
            z-index: 1; /* Asegura que esté por encima del icono */
            border: 1px solid white; /* Opcional: añade un borde blanco para separarlo */
            pointer-events: none; /* Para que no interfiera con el clic en el botón */
        }
        
        /* Estilo específico para el contador de cotizaciones */
        .quote-count {
            background-color: #3498db; /* Azul para diferenciar de DTE */
        }

        /* Asegúrate de que el contenedor del botón/icono tenga position: relative */
        .icon-container > .icon-button {
            /* ... (otros estilos existentes) ... */
            position: relative; /* Esto ya debería estar, pero confírmalo */
        }

        /* Estilos para el menú desplegable de usuario */
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 10px;
            min-width: 200px;
            z-index: 1000;
            display: none;
        }

        .icon-container > i.fa-user {
            cursor: pointer;
            position: relative;
        }

        .icon-container > i.fa-user:hover + .user-dropdown,
        .user-dropdown:hover {
            display: block;
        }

        .user-info {
            padding-bottom: 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        /* Estilos para la etiqueta de tipo de precio */
        .precio-tipo-label {
            font-size: 0.8em;
            color: #e74c3c;
            margin-left: 5px;
            font-weight: bold;
        }

        /* Estilos para el mensaje informativo de precio */
        .precio-info {
            font-size: 0.75em;
            color: #7f8c8d;
            margin-top: 3px;
            font-style: italic;
        }

        .user-name {
            display: block;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .user-role {
            display: block;
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: capitalize;
        }

        .logout-btn {
            display: flex;
            align-items: center;
            color: #e74c3c;
            text-decoration: none;
            padding: 5px 0;
            font-size: 0.9rem;
        }

        .logout-btn i {
            margin-right: 5px;
        }

        /* Animación (mantener la de la respuesta anterior) */
        @keyframes countUpdate {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .dte-count.updated {
            animation: countUpdate 0.3s ease-in-out;
        }
    </style>



</head>




<body class="table-view">
    <!-- Header principal con logo, módulos y carrito -->
    <header>
        <nav>
            <div class="logo">
                <i class="fas fa-cogs"></i>
                TATA REPUESTOS
            </div>
            <ul class="modules-menu">
                <li><a href="index.php" class="module-link"><i class="fas fa-cash-register"></i><span>Módulo Venta</span></a></li>
                <li><a href="inventory.php" class="module-link"><i class="fas fa-boxes"></i><span>Módulo Inventario</span></a></li>
                <li><a href="ventas.php" class="module-link"><i class="fas fa-chart-bar"></i><span>Módulo Reportería</span></a></li>
                <li><a href="sobres_envio.php" class="module-link"><i class="fas fa-envelope"></i><span>Módulo de Sobres</span></a></li>
                <li><a href="mod_config.php" class="module-link"><i class="fas fa-cog"></i><span>Módulo Configuración</span></a></li>
            </ul>
            <div class="cart-icon">
                <div class="icon-container">
                    <!-- Icono de Notificación -->
                    <i class="fas fa-bell"></i>

                    <!-- BOTÓN COTIZACIÓN ESTILIZADO COMO ICONO -->
                    <button id="openQuoteBtn" class="icon-button" title="Abrir Cotización">
                         <i class="fas fa-file-invoice-dollar"></i>
                         <span class="quote-count">0</span>
                    </button>
                    


                    <!-- BOTÓN DTE ESTILIZADO COMO ICONO (MANTENIENDO EL ID) -->
                    <button id="openDTEBtn" class="icon-button" title="Abrir DTE">
                         <i class="fas fa-file-invoice"></i>
                         <span class="dte-count">0</span>
                    </button>
                    <!-- El span con clase btn-text se elimina -->

                     <!-- Icono de Usuario -->
                    <i class="fas fa-user"></i>
                    <div class="user-dropdown">
                        <div class="user-info">
                            <span class="user-name"><?php echo htmlspecialchars($_SESSION['nombre']); ?></span>
                            <span class="user-role"><?php echo htmlspecialchars($_SESSION['rol']); ?></span>
                        </div>
                        <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Cerrar sesión</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>





    <!-- Sub-header con buscadores -->
    <div class="sub-header">
        <button class="filter-toggle">
            <i class="fas fa-filter"></i>
        </button>
        <div class="search-bar collapsed">
            <select id="marca_filter">
                <option value="">Marca</option>
                <option value="1">Peugeot</option>
                <option value="2">Citroën</option>
                <option value="3">Renault</option>
            </select>
            <select id="modelo_filter">
                <option value="">Modelo</option>
            </select>
            <select id="anio">
                <option value="">Año</option>
                <?php
                  for($i = date('Y'); $i >= 1990; $i--) {
                    echo "<option value='$i'>$i</option>";
                  }
                ?>
            </select>
            <select id="combustible">
                <option value="">Seleccione</option>
                <option value="Gasolina">Gasolina</option>
                <option value="Diesel">Diesel</option>
                <option value="Híbrido">Híbrido</option>
            </select>
            <select id="cilindrada">
                <option value="">Cilindrada</option>
                <option value="1000">1000</option>
                <option value="1200">1200</option>
                <option value="1400">1400</option>
                <option value="1600">1600</option>
                <option value="1800">1800</option>
                <option value="2000">2000</option>
                <option value="2500">2500</option>
                <option value="3000">3000</option>
            </select>
            <div class="search-input-wrapper">
                <input type="text" id="search-input" placeholder="Buscar por nombre...">
                <button class="search-button" id="search-icon">
                    <i class="fas fa-search"></i>
                </button>
                <button class="reset-button" id="reset-filters">
                    <i class="fas fa-undo"></i>
                </button>
            </div>
        </div>
    </div>



    <!-- Controles de vista -->
    <div class="view-controls">
        <button class="view-btn" data-view="grid">
            <i class="fas fa-th"></i> Vista Tarjetas
        </button>
        <button class="view-btn active" data-view="table">
            <i class="fas fa-list"></i> Vista Lista
        </button>
    </div>

    <?php
          require_once 'db_connection.php';

          try {
              $conn = getConnection();
              $stmt = $conn->query("SELECT
                  repuesto.id,
                  repuesto.sku,
                  repuesto.nombre,
                  repuesto.descripcion,
                  repuesto.categoria_id,
                  COALESCE(cat.nombre, 'Sin categoría') as categoria_nombre,
                  repuesto.id_subcategoria,
                  COALESCE(subcat.nombre, 'Sin subcategoría') as subcategoria_nombre,
                  repuesto.precio_venta,
                  repuesto.fabricante,
                  repuesto.stock_minimo,
                  repuesto.url_imagen,
                  COALESCE(vehiculo_compatible.combustible, 'No especificado') as combustible,
                  COALESCE(marca.nombre, 'Sin marca') as marca,
                  COALESCE(modelo.nombre, 'Sin modelo') as modelo,
                  COALESCE(vehiculo_compatible.cilindrada, 'No especificado') as cilindrada,
                  repuesto.ubicacion_tienda,
                  repuesto.codigo_fabricante,
                  SUM(COALESCE(stock.cantidad, 0)) as stock_total,
                  COUNT(DISTINCT stock.lote) as num_lotes,
                  GROUP_CONCAT(DISTINCT stock.lote SEPARATOR ', ') as lotes_disponibles
              FROM
                  repuesto
                  LEFT JOIN categoria_repuesto cat ON repuesto.categoria_id = cat.id
                  LEFT JOIN categoria_repuesto subcat ON repuesto.id_subcategoria = subcat.id
                  LEFT JOIN repuesto_compatible ON repuesto.id = repuesto_compatible.repuesto_id
                  LEFT JOIN vehiculo_compatible ON repuesto_compatible.vehiculo_id = vehiculo_compatible.id
                  LEFT JOIN modelo ON modelo.id = vehiculo_compatible.modelo_id
                  LEFT JOIN marca ON marca.id = modelo.marca_id
                  LEFT JOIN stock ON repuesto.id = stock.repuesto_id AND stock.almacen_id = 1
              WHERE repuesto.activo = 1
              GROUP BY 
                  repuesto.id,
                  repuesto.sku,
                  repuesto.nombre,
                  repuesto.descripcion,
                  repuesto.categoria_id,
                  cat.nombre,
                  repuesto.id_subcategoria,
                  subcat.nombre,
                  repuesto.precio_venta,
                  repuesto.fabricante,
                  repuesto.stock_minimo,
                  repuesto.url_imagen,
                  vehiculo_compatible.combustible,
                  marca.nombre,
                  modelo.nombre,
                  vehiculo_compatible.cilindrada,
                  repuesto.ubicacion_tienda,
                  repuesto.codigo_fabricante
              LIMIT 100");
              $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

              if ($products) {
                  // Container for both views
                  echo '<div id="products-container">';

                  // Grid View (Cards)
                  echo '<div class="products-grid">';
                  foreach($products as $product) {
                      echo '
                      <div class="product-card" data-id="' . htmlspecialchars($product['id']) . '">
                          <div class="product-image-container">
                              <img
                                  src="' . htmlspecialchars($product['url_imagen'] ?: 'images/no-image.png') . '"
                                  alt="' . htmlspecialchars($product['nombre']) . '"
                                  class="product-image"
                              >
                              <div class="image-zoom-icon" title="Ampliar imagen">
                                  <i class="fas fa-search-plus"></i>
                              </div>
                          </div>
                          <div class="product-info">
                              <div class="product-details">
                                  <div class="detail-row">
                                      <span class="detail-label">SKU:</span>
                                      <span class="product-sku">' . htmlspecialchars($product['sku']) . '</span>
                                  </div>
                                  <div class="detail-row">
                                      <span class="detail-label">Categoría:</span>
                                      <span class="product-category">' . htmlspecialchars($product['categoria_nombre']) . '</span>
                                  </div>
                                  <div class="detail-row">
                                      <span class="detail-label">Nombre:</span>
                                      <span class="product-name">' . htmlspecialchars($product['nombre']) . '</span>
                                  </div>
                                  <div class="detail-row">
                                      <span class="detail-label">Fabricante:</span>
                                      <span class="product-fabricante">' . htmlspecialchars($product['fabricante'] ?? 'No especificado') . '</span>
                                  </div>
                                  <p class="product-price">$' . number_format($product['precio_venta'], 0) . '</p>
                              </div>
                              <div class="controls-container">
                                  <div class="quantity-controls">
                                      <button class="quantity-btn minus">-</button>
                                      <span class="quantity-display">0</span>
                                      <button class="quantity-btn plus">+</button>
                                  </div>
                                  <div class="button-group">
                                      <button class="add-to-cart-btn" title="Agregar al carrito">
                                         <i class="fas fa-cart-plus"></i>
                                     </button>
                                      <button class="add-to-quote-btn" onclick="addToQuote(\'' . $product['id'] . '\', \'' . htmlspecialchars(addslashes($product['nombre'])) . '\', \'' . $product['precio_venta'] . '\')" title="Agregar a cotización">
                                          <i class="fas fa-file-invoice-dollar"></i>
                                      </button>
                                  </div>
                              </div>
                          </div>
                      </div>';
                  }
                  echo '</div>';

                  // Table View
                  echo '<div class="products-table">
                      <table>
                          <thead>
                              <tr>
                                  <th data-col="sku">SKU</th>
                                  <th data-col="nombre">Nombre</th>
                                  <th data-col="categoria">Categoría</th>
                                  <th data-col="subcategoria">Subcategoría</th>
                                  <th data-col="descripcion">Descripción</th>
                                  <th data-col="precio">Precio</th>
                                  <th data-col="fabricante">Fabricante</th>
                                  <th data-col="stock_total">Stock Disponible</th>
                                  <th data-col="cantidad">Cantidad</th>
                                  <th data-col="acciones">Acciones</th>
                              </tr>
                          </thead>
                          <tbody>';

                  foreach($products as $product) {
                      echo '<tr data-id="' . htmlspecialchars($product['id']) . '">
                          <td>' . htmlspecialchars($product['sku']) . '</td>
                          <td>' . htmlspecialchars($product['nombre']) . '</td>
                          <td>' . htmlspecialchars($product['categoria_nombre']) . '</td>
                          <td>' . htmlspecialchars($product['subcategoria_nombre']) . '</td>
                          <td class="description-cell" title="' . htmlspecialchars($product['descripcion']) . '">' . htmlspecialchars($product['descripcion']) . '</td>
                          <td>$' . number_format($product['precio_venta'] ?? 0, 0) . '</td>
                          <td>' . htmlspecialchars($product['fabricante'] ?? 'No especificado') . '</td>
                          <td>' . htmlspecialchars($product['stock_total'] ?? 0) . '</td>
                          <td>
                              <div class="quantity-controls">
                                  <button class="quantity-btn minus">-</button>
                                  <span class="quantity-display">0</span>
                                  <button class="quantity-btn plus">+</button>
                              </div>
                          </td>
                          <td data-col="acciones">
                              <div class="button-group">
                                  <button class="add-to-cart-btn" title="Agregar al carrito">
                                      <i class="fas fa-cart-plus"></i>
                                  </button>
                                  <button class="add-to-quote-btn" onclick="addToQuote(\'' . $product['id'] . '\', \'' . htmlspecialchars(addslashes($product['nombre'])) . '\', \'' . $product['precio_venta'] . '\')" title="Agregar a cotización">
                                      <i class="fas fa-file-invoice-dollar"></i>
                                  </button>
                              </div>
                          </td>
                      </tr>';
                  }

                  echo '</tbody></table></div>';
                  echo '</div>'; // Close products-container
              } else {
                  echo '<div class="no-products">No se encontraron productos</div>';
              }
          } catch(Exception $e) {
              echo $e->getMessage();
          }
          ?>

    <!-- Modal del carrito -->
    <div class="cart-overlay"></div>
    <div class="cart-modal">
        <div class="cart-header">
            <h2>Carrito de Compras</h2>
            <button class="close-cart">&times;</button>
        </div>

        <!-- Etapa 1: Lista de productos -->
        <div id="cart-stage-1" class="cart-stage active">
            <div class="search-realtime">
                <input type="text" id="search-realtime" placeholder="Ingrese SKU para agregar al carrito" class="modern-input">
            </div>
            <div class="cart-items"></div>
            <div class="cart-total">
                <h3>Total: $<span id="total-general">0.00</span></h3>
            </div>
            <div class="cart-actions">
                <button onclick="goToStage2()" class="checkout-btn">Continuar con la compra</button>
            </div>
        </div>

        <!-- Etapa 2: Checkout form -->
        <div id="cart-stage-2" class="cart-stage">
            <button onclick="goToStage1()" class="back-btn">
                <i class="fas fa-arrow-left"></i> Volver al carrito
            </button>
            <div class="checkout-form">
                <div class="client-section">
                    <input type="text" id="client_rut" placeholder="Ingrese RUT del cliente" class="modern-input">
                    <button onclick="checkClient()" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div id="client_info"></div>

                <select id="document_type" class="modern-select">
                    <option value="boleta">Boleta</option>
                    <option value="factura">Factura</option>
                </select>
                <input type="text" id="document_number" placeholder="Número de documento" class="modern-input">
                <select id="payment_conditions" class="modern-select">
                    <option value="">Condición de pago</option>
                    <option value="contado">Contado</option>
                    <option value="credito">Crédito</option>
                </select>

                <button type="button" onclick="togglePaymentFields()" class="checkout-btn" style="margin-bottom: 10px;">
                    <i class="fas fa-chevron-down"></i> Pedidos de compra
                </button>

                <div id="payment-fields" style="display: none;">
                    <div style="margin: 10px 0;">
                        <label for="fecha_compromiso">Fecha Compromiso:</label>
                        <input type="date" id="fecha_compromiso" class="modern-input">
                    </div>
                    <div style="margin: 10px 0;">
                        <label for="abono">Abono:</label>
                        <input type="number" id="abono" class="modern-input" min="0" step="1" value="0">
                    </div>
                </div>

                <div class="cart-actions">
                    <button onclick="processSale()" class="checkout-btn">
                        <i class="fas fa-check"></i> Finalizar Venta
                    </button>
                    <button onclick="printQuote()" class="checkout-btn print-btn">
                        <i class="fas fa-print"></i> Imprimir Cotización
                    </button>
                </div>
            </div>
        </div>
    </div>

<!-- Canvas para el formulario DTE -->
<div class="canvas-overlay" id="dteOverlay">
</div>

<div class="dte-canvas" id="dteCanvas">
    <div class="dte-header">
        <h2>Generar Documento Tributario Electrónico (DTE)</h2>
        <button class="close-canvas" id="closeDTEBtn">&times;</button>
    </div>

    <form id="dteForm">
        <!-- Identificación DTE -->
        <div class="dte-form-section">
            <h3>Identificación DTE</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="tipoDTE">Tipo DTE</label>
                    <select id="tipoDTE" name="tipoDTE" class="modern-select" required
                        onchange="toggleDTEFields(); fetchFolio(); handleReceptorFields();">
                        <option value="39" selected>Boleta (39)</option>
                        <option value="33">Factura Electrónica (33)</option>
                        <option value="34">Factura Exenta (34)</option>
                        <option value="61">Nota de Crédito (61)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="folioDTE">Folio</label>
                    <input type="number" id="folioDTE" name="folioDTE" class="modern-input" required value="52">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="fechaEmision">Fecha Emisión</label>
                    <input type="date" id="fechaEmision" name="fechaEmision" class="modern-input" required>
                </div>
                <div class="form-group factura-field">
                    <label for="fechaVencimiento">Fecha Vencimiento</label>
                    <input type="date" id="fechaVencimiento" name="fechaVencimiento" class="modern-input">
                </div>
                <div class="form-group factura-field">
                    <label for="formaPago">Forma de Pago</label>
                    <select id="formaPago" name="formaPago" class="modern-select">
                        <option value="1">Contado (1)</option>
                        <option value="2">Crédito (2)</option>
                        <option value="3">Sin costo (3)</option>
                    </select>
                </div>
                <div class="form-group boleta-field" style="display:none;">
                    <label for="indicadorServicio">Indicador Servicio</label>
                    <select id="indicadorServicio" name="indicadorServicio" class="modern-select">
                        <option value="1">1º Categoría</option>
                        <option value="2">2º Categoría</option>
                        <option value="3" selected>Boleta (3)</option>
                        <option value="4">Transporte pasajeros (4)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Emisor (Oculto en pantalla, pero con valores disponibles para desarrollo) -->
        <div class="dte-form-section" style="display: none;">
            <h3>Emisor</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="rutEmisor">RUT Emisor</label>
                    <input type="text" id="rutEmisor" name="rutEmisor" class="modern-input readonly-field" readonly
                        value="78078979-4">
                </div>
                <div class="form-group factura-field">
                    <label for="razonSocialEmisor">Razón Social</label>
                    <input type="text" id="razonSocialEmisor" name="razonSocialEmisor"
                        class="modern-input readonly-field" readonly value="Tata repuestos Automotrices Limitada">
                </div>
                <div class="form-group boleta-field" style="display:none;">
                    <label for="razonSocialBoleta">Razón Social Boleta</label>
                    <input type="text" id="razonSocialBoleta" name="razonSocialBoleta"
                        class="modern-input readonly-field" readonly value="Tata repuestos Automotrices Limitada">
                </div>
            </div>
            <div class="form-group factura-field">
                <label for="giroEmisor">Giro</label>
                <input type="text" id="giroEmisor" name="giroEmisor" class="modern-input readonly-field" readonly
                    value="Ventas de partes, piezas y accesorios para vehículos automotores">
            </div>
            <div class="form-group boleta-field" style="display:none;">
                <label for="giroBoleta">Giro Boleta</label>
                <input type="text" id="giroBoleta" name="giroBoleta" class="modern-input readonly-field" readonly
                    value="Ventas de partes, piezas y accesorios para vehículos automotores">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="direccionEmisor">Dirección</label>
                    <input type="text" id="direccionEmisor" name="direccionEmisor" class="modern-input readonly-field"
                        readonly value="Manuel montt 1286, Local 2">
                </div>
                <div class="form-group">
                    <label for="comunaEmisor">Comuna</label>
                    <input type="text" id="comunaEmisor" name="comunaEmisor" class="modern-input readonly-field"
                        readonly value="Temuco">
                </div>
                <div class="form-group factura-field">
                    <label for="ciudadEmisor">Ciudad</label>
                    <input type="text" id="ciudadEmisor" name="ciudadEmisor" class="modern-input readonly-field"
                        readonly value="Temuco">
                </div>
            </div>
        </div>

          <!-- Referencias (solo para Nota de Crédito) -->
        <div class="dte-form-section" id="referenciasSection" style="display: none;">
            <h3>Referencias</h3>

            <!-- Primera fila: Fecha y Tipo de Documento -->
            <div class="form-row">

                <div class="form-group">
                    <label for="folioRef">Folio Referencia</label>
                    <div class="input-with-button">
                        <input type="number" id="folioRef" class="modern-input" required>
                        <button type="button" id="buscarFolioBtn" class="modern-button" title="Buscar documento">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div id="folioRefMessage" style="margin-top: 5px; font-size: 12px; display: none;"></div>
                </div>
                <div class="form-group">
                    <label for="fechaDocRef">Fecha Documento Referencia</label>
                    <input type="date" id="fechaDocRef" class="modern-input" required>
                </div>

            </div>

            <!-- Segunda fila: Folio y Código de Referencia -->
            <div class="form-row">
                <div class="form-group">
                    <label for="tipoDocRef">Tipo Documento</label>
                    <select id="tipoDocRef" class="modern-select" required>
                        <option value="33">Factura Electrónica (33)</option>
                        <option value="34">Factura Exenta Electrónica (34)</option>
                        <option value="39">Boleta Electrónica (39)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="codigoRef">Código Referencia</label>
                    <select id="codigoRef" class="modern-select" required>
                        <option value="1">Anula Documento de Referencia (1)</option>
                        <option value="2">Corrige Texto Documento Referencia (2)</option>
                        <option value="3">Corrige Montos (3)</option>
                    </select>
                </div>
            </div>

            <!-- Tercera fila: Razón de Referencia (campo completo) -->
            <div class="form-row">
                <div class="form-group" style="width: 100%;">
                    <label for="razonRef">Razón Referencia</label>
                    <input type="text" id="razonRef" class="modern-input" required>
                </div>
            </div>
        </div>

        <!-- Receptor -->
        <div class="dte-form-section">
            <h3>Receptor</h3>

            <!-- Checkbox for Boleta default receptor - Only visible for Boleta -->
            <div class="form-group boleta-field" style="margin-bottom: 15px;">
                <label class="checkbox-container">
                    <input type="checkbox" id="defaultReceptorCheckbox">
                    <span class="checkbox-label">Usar receptor genérico (66666666-6)</span>
                </label>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="rutReceptor">RUT Receptor</label>
                    <div class="input-with-button">
                        <input type="text" id="rutReceptor" name="rutReceptor" class="modern-input"
                            placeholder="Ej: 12345678-9" required onblur="validarFormatoRUT(this)">
                        <button type="button" id="buscarReceptorBtn" class="modern-button" title="Buscar receptor"
                            onclick="buscarReceptor()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div id="rutError" class="error-message" style="color: red; display: none;"></div>
                    <div id="receptorSearchMessage" style="margin-top: 5px; font-size: 12px; display: none;"></div>
                </div>
                <div class="form-group">
                    <label for="razonSocialReceptor">Razón Social</label>
                    <input type="text" id="razonSocialReceptor" name="razonSocialReceptor" class="modern-input"
                        required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="direccionReceptor">Dirección</label>
                    <input type="text" id="direccionReceptor" name="direccionReceptor" class="modern-input" required>
                </div>
                <div class="form-group">
                    <label for="comunaReceptor">Comuna</label>
                    <input type="text" id="comunaReceptor" name="comunaReceptor" class="modern-input" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group factura-field">
                    <label for="giroReceptor">Giro</label>
                    <input type="text" id="giroReceptor" name="giroReceptor" class="modern-input">
                </div>
                <div class="form-group">
                    <label for="contactoReceptor">Contacto</label>
                    <input type="text" id="contactoReceptor" name="contactoReceptor" class="modern-input"
                        placeholder="Teléfono">
                </div>
            </div>
        </div>

        <!-- Detalles de items -->
        <div class="dte-form-section">
            <h3>Detalles</h3>
            <div id="itemsContainer">
                <div class="item-row">
                    <button type="button" class="remove-item-btn"><i class="fas fa-times"></i></button>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="nombre_0">Nombre producto</label>
                            <input type="text"
                                   id="nombre_0"
                                   class="modern-input item-nombre"
                                   required
                                   value=""
                                   pattern="[^:\/&quot;&,@]*"
                                   oninput="validateProductField(this)"
                                   title="No se permiten los caracteres: : / & &#34; , @">
                        </div>
                        <div class="form-group">
                            <label for="cantidad_0">Cantidad</label>
                            <input type="number" id="cantidad_0" class="modern-input item-cantidad" required value="1"
                                min="1" step="1" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="descripcion_0">Descripción producto</label>
                        <input type="text"
                               id="descripcion_0"
                               class="modern-input item-descripcion"
                               pattern="[^:\/&quot;&,@]*"
                               oninput="validateProductField(this)"
                               title="No se permiten los caracteres: : / & &#34; , @">
                    </div>
                    <div class="form-row">
                        <div class="form-group" style="width: 15%;">
                            <label for="unidad_0">Unidad</label>
                            <input type="text" id="unidad_0" class="modern-input item-unidad" value="un" style="width: 100%;">
                        </div>
                        <div class="form-group" style="width: 40%;">
                            <label for="precio_0">Precio <span id="precio-tipo-label" class="precio-tipo-label">(Neto)</span></label>
                            <input type="number" id="precio_0" class="modern-input item-precio" required value="0"
                                min="0" onchange="calcularMontoItem(this); actualizarMontoNeto();">
                            <div id="precio-info" class="precio-info">En facturas, el precio es neto (sin IVA)</div>
                        </div>
                        <div class="form-group" style="width: 40%; border: 2px solid #4e73df; border-radius: 6px; padding: 10px; background-color: #f8f9fc;">
                            <label for="precio_total_0" style="color: #4e73df; font-weight: bold;">Precio Total</label>
                            <input type="number" id="precio_total_0" class="modern-input item-precio-total" value="0"
                                min="0" onchange="calcularPrecioNeto(this);" oninput="calcularPrecioNeto(this);">
                            <div class="precio-info" style="color: #4e73df;">Ingrese el valor total para calcular el precio neto en facturas</div>
                        </div>

                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="descuento_0">Descuento</label>
                            <input type="number" id="descuento_0" class="modern-input item-descuento" value="0" min="0"
                                onchange="calcularMontoItem(this); actualizarMontoNeto();">
                        </div>
                        <div class="form-group">
                            <label for="recargo_0">Recargo</label>
                            <input type="number" id="recargo_0" class="modern-input item-recargo" value="0" min="0"
                                onchange="calcularMontoItem(this); actualizarMontoNeto();">
                        </div>
                        <div class="form-group">
                            <label for="montoItem_0">Monto Item</label>
                            <input type="number" id="montoItem_0" class="modern-input item-monto" value="19328"
                                readonly>
                        </div>
                    </div>
                </div>
            </div>
            <button type="button" id="addItemBtn" class="add-item-btn">
                <i class="fas fa-plus"></i> Agregar otro item
            </button>
        </div>

        <!-- Totales -->
        <div class="dte-form-section">
            <h3>Totales</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="montoNeto">Monto Neto</label>
                    <input type="number" id="montoNeto" name="montoNeto" class="modern-input" required value="0"
                        onchange="calcularTotales()">
                </div>
                <div class="form-group">
                    <label for="tasaIVA">Tasa IVA (%)</label>
                    <input type="number" id="tasaIVA" name="tasaIVA" class="modern-input" required value="19" readonly>
                </div>
                <div class="form-group">
                    <label for="ivaCalculado">IVA</label>
                    <input type="number" id="ivaCalculado" name="ivaCalculado" class="modern-input" readonly value="0">
                </div>
                <div class="form-group">
                    <label for="montoTotal">Monto Total</label>
                    <input type="number" id="montoTotal" name="montoTotal" class="modern-input" readonly value="0">
                </div>
            </div>
        </div>

        <!-- Información de Inventario -->
        <div class="dte-form-section">
            <div class="section-header-with-toggle" style="display: flex; justify-content: space-between; align-items: center;">
                <h3>Información de Inventario</h3>
                <label class="checkbox-container">
                    <input type="checkbox" id="enableInventarioCheckbox" class="inventario-toggle" checked>
                    <span class="checkbox-label">Actualizar inventario</span>
                </label>
            </div>
            <div id="inventarioFields">
                <div class="form-row">
                    <div class="form-group">
                        <label for="almacen_id">Almacén</label>
                        <select id="almacen_id" name="almacen_id" class="modern-select">
                            <option value="1">Almacén Principal</option>
                            <!-- Aquí se pueden agregar más almacenes dinámicamente -->
                        </select>
                    </div>
                </div>
                <div class="info-message" style="margin-top: 10px;">
                    <i class="fas fa-info-circle"></i>
                    <p>Al generar el DTE, se registrarán automáticamente las salidas de inventario para los productos con ID.</p>
                </div>
            </div>
        </div>

        <!-- Descuentos y Recargos (solo para factura) -->
        <div class="dte-form-section factura-field">
            <div class="section-header-with-toggle"
                style="display: flex; justify-content: space-between; align-items: center;">
                <h3>Descuentos y Recargos Globales</h3>
                <label class="checkbox-container">
                    <input type="checkbox" id="enableDescuentosCheckbox" class="descuentos-recargos-toggle"
                        onchange="toggleDescuentosRecargos()">
                    <span class="checkbox-label">Habilitar sección</span>
                </label>
            </div>
            <div id="descuentosRecargosFields">
                <div class="form-row">
                    <div class="form-group">
                        <label for="tipoMov">Tipo</label>
                        <select id="tipoMov" name="tipoMov" class="modern-select">
                            <option value="">Seleccione</option>
                            <option value="Descuento">Descuento</option>
                            <option value="Recargo">Recargo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="descTipoValor">Tipo de Valor</label>
                        <select id="descTipoValor" name="descTipoValor" class="modern-select">
                            <option value="">Seleccione</option>
                            <option value="Pesos">Pesos</option>
                            <option value="Porcentaje">Porcentaje</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="descValor">Valor</label>
                        <input type="number" id="descValor" name="descValor" class="modern-input" value="" min="0">
                    </div>
                </div>
                <div class="form-group">
                    <label for="descDescripcion">Descripción</label>
                    <input type="text" id="descDescripcion" name="descDescripcion" class="modern-input" value="">
                </div>
            </div>
        </div>

        <!-- Certificado -->
        <div class="dte-form-section" style="display: none;">
            <h3>Certificado</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="rutCertificado">RUT</label>
                    <input type="text" id="rutCertificado" name="rutCertificado" class="modern-input" required
                        value="17365958-K">
                </div>
                <div class="form-group">
                    <label for="passwordCertificado">Password</label>
                    <input type="password" id="passwordCertificado" name="passwordCertificado" class="modern-input"
                        required value="1569">
                </div>
            </div>
        </div>



        <!-- Contenedor de todos los botones -->
        <div class="buttons-container" style="display: flex; flex-direction: column; gap: 20px;">
            <!-- Primera fila de botones (con marco pero sin etiqueta) -->
            <div class="buttons-row-wrapper" style="
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    padding: 20px;
                ">
                <div class="buttons-row" style="display: flex; gap: 10px;">
                    <button type="button" id="generateAndSendBtn" class="json-btn" style="background-color: #28a745;">
                        <i class="fas fa-paper-plane"></i> Generar DTE
                    </button>

                    <button type="button" id="printPdfBtn" class="json-btn"
                        style="background-color: #007bff; display: none;">
                        <i class="fas fa-print"></i> Imprimir documento
                    </button>
                    
                    
                    
                  
                </div>
            </div>

            <!-- Segunda fila de botones (con marco y etiqueta) -->
            <div class="admin-buttons-wrapper" style="
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    padding: 20px;
                    position: relative;
                ">
                <!-- Etiqueta de administrador -->
                <div class="admin-label" style="
                        position: absolute;
                        top: -12px;
                        left: 20px;
                        background-color: #dc3545;
                        color: white;
                        padding: 2px 15px;
                        border-radius: 15px;
                        font-size: 0.85em;
                        font-weight: bold;
                    ">
                    <i class="fas fa-lock"></i> Solo uso de administrador
                </div>

                <div class="buttons-row" style="display: flex; gap: 10px;">
                    <!-- <button type="button" id="requestFoliosBtn" class="json-btn" style="background-color: #f39c12;">
                        <i class="fas fa-download"></i> Solicitar Folios CAF
                    </button> -->

                    <button type="submit" class="json-btn">
                        <i class="fas fa-file-code"></i> Generar JSON
                    </button>

                    <!-- Nuevo botón para simular venta -->
                    <button type="button" id="simularVentaBtn" class="json-btn" style="background-color: #f39c12;">
                        <i class="fas fa-boxes"></i> Simular Venta (Solo Inventario)
                    </button>

                    <!-- Botón para limpiar formulario -->
                    <button type="button" id="limpiarFormBtn" class="json-btn" style="background-color: #dc3545;">
                        <i class="fas fa-broom"></i> Limpiar Formulario
                    </button>
                </div>
            </div>
        </div>

    </form>

    <div id="jsonResultContainer">
        <div id="jsonResult"></div>
        <div class="json-actions" style="display: none;">
            <button class="json-copy-btn" id="copyJsonBtn">
                <i class="fas fa-copy"></i> Copiar JSON
            </button>
            <span class="copy-message" id="copyMessage">¡Copiado al portapapeles!</span>
        </div>

        <!-- Agregar este HTML después del div jsonResultContainer -->
        <div id="enviarDTEContainer" style="display: none; margin-top: 30px;">
            <div class="dte-form-section">
                <h3>Enviar DTE a la API</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="apiToken">Token de Autorización (predefinido)</label>
                        <input type="password" id="apiToken" class="modern-input" value="2037-N680-6391-2493-5987"
                            readonly>
                    </div>
                </div>
                <div class="file-paths-info">
                    <p><i class="fas fa-file-alt"></i> <strong>Archivos predefinidos:</strong></p>
                    <ul>
                        <li><strong>Certificado:</strong> Documents/17365958-K.pfx</li>
                        <li><strong>Folios:</strong> Documents/folios/Facturas/folios_facturas_10</li>
                    </ul>
                </div>
                <button type="button" id="enviarDTEBtn" class="json-btn">
                    <i class="fas fa-paper-plane"></i> Enviar DTE
                </button>

            </div>

            <!-- Indicador de carga -->
            <div id="loadingIndicator"
                style="display: none; justify-content: center; align-items: center; margin: 20px 0;">
                <div class="spinner">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                </div>
                <span style="margin-left: 10px;">Enviando DTE, por favor espere...</span>
            </div>

            <!-- Contenedor de respuesta de la API -->
            <div id="responseContainer" class="dte-form-section" style="display: none; margin-top: 20px;">
                <h3>Información de Envío</h3>
                <div id="apiResponse"></div>
            </div>
        </div>
    </div>
</div>

<!-- Canvas para Cotizaciones -->
<div class="canvas-overlay" id="quoteOverlay"></div>
<div class="quote-canvas" id="quoteCanvas">
    <div class="quote-header">
        <button id="backQuoteBtn" class="back-btn" style="display:none;"><i class="fas fa-arrow-left"></i></button>
        <h2 id="quoteTitle"><i class="fas fa-file-invoice-dollar"></i> Generar Cotización</h2>
        <button class="close-canvas" id="closeQuoteBtn">&times;</button>
    </div>
    
    <!-- Contenedor deslizante con múltiples niveles -->
    <div class="quote-slide-container">
        <div class="quote-slide-inner">
            <!-- Nivel 1: Formulario de cotización -->
            <div class="quote-level" id="quoteFormLevel">
                <!-- Botones de acción en la parte superior -->
                <div class="quote-actions-top">
                    <button class="action-button secondary" onclick="clearQuote()">
                        <i class="fas fa-eraser"></i> Limpiar
                    </button>
                    <button class="action-button save" onclick="saveQuote()">
                        <i class="fas fa-floppy-disk"></i> Guardar
                    </button>
                    <button class="action-button primary" onclick="generateQuote()">
                        <i class="fas fa-file-export"></i> Generar PDF
                    </button>
                    <button class="action-button info" onclick="openQuotesList()">
                        <i class="fas fa-clipboard-list"></i> Ver Cotizaciones
                    </button>
                </div>
                
                <div class="quote-content">
        <!-- Información del Cliente -->
        <div class="quote-form-section">
            <h3><i class="fas fa-user"></i> Información del Cliente</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="quoteClientName"><i class="fas fa-user-circle"></i> Nombre del Cliente</label>
                    <input type="text" id="quoteClientName" class="modern-input" placeholder="Nombre completo">
                </div>
                <div class="form-group">
                    <label for="quoteClientRut"><i class="fas fa-id-card"></i> RUT</label>
                    <input type="text" id="quoteClientRut" class="modern-input" placeholder="12.345.678-9">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="quoteClientEmail"><i class="fas fa-envelope"></i> Email</label>
                    <input type="email" id="quoteClientEmail" class="modern-input" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="quoteClientPhone"><i class="fas fa-phone"></i> Teléfono</label>
                    <input type="tel" id="quoteClientPhone" class="modern-input" placeholder="+56 9 1234 5678">
                </div>
            </div>
        </div>

        <!-- Opciones de agregar productos -->
        <div class="quote-form-section">
            <h3><i class="fas fa-box-open"></i> Agregar Productos</h3>
            <div class="tabs-container">
                <button class="tab-button active" data-tab="manual">Ingreso Manual</button>
                <button class="tab-button" data-tab="search">Buscar en Inventario</button>
            </div>
            
            <!-- Tab de ingreso manual -->
            <div class="tab-content active" id="manual-tab">
                <div class="form-row">
                    <div class="form-group" style="width: 100%;">
                        <label for="manualProductName"><i class="fas fa-tag"></i> Nombre del Producto</label>
                        <input type="text" id="manualProductName" class="modern-input" placeholder="Nombre del producto">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="manualProductPrice"><i class="fas fa-dollar-sign"></i> Precio Unitario</label>
                        <input type="number" id="manualProductPrice" class="modern-input" placeholder="0" min="0">
                    </div>
                    <div class="form-group">
                        <label for="manualProductQuantity"><i class="fas fa-boxes"></i> Cantidad</label>
                        <input type="number" id="manualProductQuantity" class="modern-input" placeholder="1" min="1" value="1">
                    </div>
                    <div class="form-group">
                        <button class="action-button" onclick="addManualProduct()">
                            <i class="fas fa-plus"></i> Agregar
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Tab de búsqueda -->
            <div class="tab-content" id="search-tab">
                <div class="form-group">
                    <label for="quoteProductSearch"><i class="fas fa-search"></i> Buscar Producto</label>
                    <input type="text" id="quoteProductSearch" class="modern-input" placeholder="Buscar por nombre, SKU o código...">
                </div>
                <div class="search-results" id="quoteSearchResults">
                    <!-- Resultados de búsqueda se mostrarán aquí -->
                </div>
            </div>
        </div>

        <!-- Lista de productos agregados -->
        <div class="quote-form-section">
            <h3><i class="fas fa-receipt"></i> Productos en la Cotización</h3>
            <div class="quote-items-container" id="quoteItemsContainer">
                <div class="empty-state">
                    <i class="fas fa-box-open"></i>
                    <p>No hay productos agregados a la cotización</p>
                </div>
            </div>
            
            <!-- Totales -->
            <div class="quote-totals" id="quoteTotals" style="display: none;">
                <div class="total-row">
                    <span>Subtotal:</span>
                    <span id="quoteSubtotal">$0</span>
                </div>
                <div class="total-row total-final">
                    <span>Total:</span>
                    <span id="quoteTotal">$0</span>
                </div>
            </div>
        </div>

                    <!-- Notas adicionales -->
                    <div class="quote-form-section">
                        <h3><i class="fas fa-clipboard"></i> Notas Adicionales</h3>
                        <div class="form-group">
                            <textarea id="quoteNotes" class="modern-input" rows="3" placeholder="Condiciones de pago, validez de la cotización, etc."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Nivel 2: Lista de cotizaciones -->
            <div class="quote-level" id="quoteListLevel">
                <div class="quote-list-content">
                    <!-- Barra de búsqueda -->
                    <div class="quote-search-bar">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search"></i>
                            <input type="text" id="quoteSearchInput" class="modern-input" placeholder="Buscar cotización por número, cliente o fecha...">
                        </div>
                    </div>
                    <!-- Lista de cotizaciones -->
                    <div class="quotes-list-container" id="quotesListContainer">
                        <!-- Las cotizaciones se cargarán aquí dinámicamente -->
                    </div>
                </div>
            </div>
            
            <!-- Nivel 3: Detalle de cotización -->
            <div class="quote-level" id="quoteDetailLevel">
                <div class="quote-detail-content" id="quoteDetailContent">
                    <!-- El detalle se cargará aquí dinámicamente -->
                </div>
            </div>
        </div>
    </div>
</div>



    <script src="js/app.js"></script>
    <script src="js/index.js"></script>
    <script src="js/view-switcher.js"></script>
    <script src="js/header-search.js"></script>
    <script src="js/image-viewer.js"></script>
    <script src="js/buscar-documento.js"></script>
    <script src="js/user-dropdown.js"></script>
    <script src="js/solicitar_folios_auto.js"></script>
    <script src="js/dte_productos.js"></script>
    <script src="js/dte-fields.js"></script>
    <script src="js/dte-button-fix.js"></script>
    <script src="js/dte-form-cleaner.js"></script>
    <script src="js/quote-canvas.js"></script>
    <script src="js/quotes-list.js"></script>
    <script src="js/simular-venta.js"></script>
    <script src="js/logout-handler.js"></script>
    <script>
    // Asegurarse de que solo se muestre una vista al cargar la página
    document.addEventListener('DOMContentLoaded', function() {
        // Esperar un momento para asegurarnos de que view-switcher.js ya se ha ejecutado
        setTimeout(function() {
            // Establecer la vista de tabla por defecto si no hay una vista guardada
            if (!window.currentView) {
                if (typeof setActiveView === 'function') {
                    setActiveView('table');
                }
            }

            // Enfocar el campo de búsqueda
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }, 100);
        
    });
    </script>
    <script>
function validateProductField(input) {
    // Caracteres prohibidos (sin incluir el guion)
    const prohibitedChars = [':', '/', '&', '"', ',', '@'];

    let value = input.value;
    let hasProhibited = false;

    // Revisar cada caracter prohibido
    for (let char of prohibitedChars) {
        if (value.includes(char)) {
            hasProhibited = true;
            value = value.split(char).join(''); // Eliminar el caracter prohibido
        }
    }

    // Si se encontraron caracteres prohibidos, actualizar el valor y mostrar alerta
    if (hasProhibited) {
        input.value = value;
        alert('Los siguientes caracteres no están permitidos: : / & " , @');
    }
}

// Aplicar la validación a los campos dinámicos nuevos
document.getElementById('addItemBtn').addEventListener('click', function() {
    // Esperar a que el DOM se actualice
    setTimeout(() => {
        const newInputs = document.querySelectorAll('.item-nombre, .item-descripcion');
        newInputs.forEach(input => {
            input.pattern = "[^:\/&quot;&,@]*";
            input.title = "No se permiten los caracteres: : / & \" , @";
            input.addEventListener('input', function() {
                validateProductField(this);
            });
        });
    }, 100);
});

// Script adicional para asegurar que el receptor genérico funcione
document.addEventListener('DOMContentLoaded', function() {
    console.log('Script adicional para receptor genérico cargado');

    // Función para aplicar receptor genérico solo para boletas
    function aplicarReceptorGenerico() {
        console.log('Verificando tipo de documento desde script adicional');

        const tipoDTE = document.getElementById('tipoDTE');
        const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');

        if (tipoDTE && defaultReceptorCheckbox) {
            if (tipoDTE.value === '39') {
                // Solo para boletas
                console.log('Es boleta - Aplicando receptor genérico');

                defaultReceptorCheckbox.checked = true;

                // Completar campos
                const rutInput = document.getElementById('rutReceptor');
                const razonSocialInput = document.getElementById('razonSocialReceptor');
                const direccionInput = document.getElementById('direccionReceptor');
                const comunaInput = document.getElementById('comunaReceptor');
                const contactoInput = document.getElementById('contactoReceptor');

                if (rutInput) {
                    rutInput.value = "66666666-6";
                    rutInput.readOnly = true;
                    rutInput.classList.add('readonly-field');
                }
                if (razonSocialInput) {
                    razonSocialInput.value = "Cliente Boleta";
                    razonSocialInput.readOnly = true;
                    razonSocialInput.classList.add('readonly-field');
                }
                if (direccionInput) {
                    direccionInput.value = "Temuco";
                    direccionInput.readOnly = true;
                    direccionInput.classList.add('readonly-field');
                }
                if (comunaInput) {
                    comunaInput.value = "Temuco";
                    comunaInput.readOnly = true;
                    comunaInput.classList.add('readonly-field');
                }
                if (contactoInput) {
                    contactoInput.value = "";
                }

                console.log('Receptor genérico aplicado para boleta');
            } else {
                // Para cualquier otro tipo de documento
                console.log('No es boleta - Limpiando campos del receptor');

                defaultReceptorCheckbox.checked = false;

                // Limpiar y habilitar campos
                const campos = ['rutReceptor', 'razonSocialReceptor', 'direccionReceptor', 'comunaReceptor', 'contactoReceptor', 'giroReceptor'];

                campos.forEach(campoId => {
                    const campo = document.getElementById(campoId);
                    if (campo) {
                        console.log('Limpiando campo:', campoId);
                        campo.value = "";
                        campo.readOnly = false;
                        campo.disabled = false;
                        campo.removeAttribute('readonly');
                        campo.removeAttribute('disabled');
                        campo.classList.remove('readonly-field');
                        campo.style.backgroundColor = '';
                        campo.style.cursor = '';
                        campo.style.pointerEvents = '';
                    }
                });
            }
        }
    }

    // Aplicar al cargar la página si es boleta
    setTimeout(aplicarReceptorGenerico, 500);

    // Aplicar cuando se abra el canvas DTE
    const openDTEBtn = document.getElementById('openDTEBtn');
    if (openDTEBtn) {
        openDTEBtn.addEventListener('click', function() {
            setTimeout(aplicarReceptorGenerico, 300);
        });
    }

    // Aplicar cuando cambie el tipo de DTE
    const tipoDTESelect = document.getElementById('tipoDTE');
    if (tipoDTESelect) {
        tipoDTESelect.addEventListener('change', function() {
            setTimeout(aplicarReceptorGenerico, 100);
        });
    }
});

// Función global para manejar los campos del receptor según el tipo de DTE
function handleReceptorFields() {
    console.log('handleReceptorFields ejecutándose');

    const tipoDTE = document.getElementById('tipoDTE').value;
    const defaultReceptorCheckbox = document.getElementById('defaultReceptorCheckbox');

    console.log('Tipo DTE actual:', tipoDTE);

    if (tipoDTE === '39') {
        // Para boletas: activar receptor genérico
        console.log('Activando receptor genérico para boleta');
        if (defaultReceptorCheckbox) {
            defaultReceptorCheckbox.checked = true;
        }
        aplicarReceptorGenerico();
    } else {
        // Para otros tipos: limpiar y habilitar campos
        console.log('Limpiando campos para tipo:', tipoDTE);
        if (defaultReceptorCheckbox) {
            defaultReceptorCheckbox.checked = false;
        }

        // Usar la función de forzar habilitación si está disponible
        if (typeof forceEnableReceptorFields === 'function') {
            forceEnableReceptorFields();
        } else {
            // Fallback: limpiar y habilitar todos los campos del receptor manualmente
            const campos = ['rutReceptor', 'razonSocialReceptor', 'direccionReceptor', 'comunaReceptor', 'contactoReceptor', 'giroReceptor'];

            campos.forEach(campoId => {
                const campo = document.getElementById(campoId);
                if (campo) {
                    console.log('Limpiando campo:', campoId);
                    campo.value = "";
                    campo.readOnly = false;
                    campo.disabled = false;
                    campo.removeAttribute('readonly');
                    campo.removeAttribute('disabled');
                    campo.classList.remove('readonly-field');
                    campo.style.backgroundColor = '';
                    campo.style.cursor = '';
                    campo.style.pointerEvents = '';
                }
            });
        }

        console.log('Campos habilitados para ingreso manual');
    }
}

// Prevenir FOUC - Mostrar el contenido cuando todo esté cargado
window.addEventListener('load', function() {
    document.body.classList.add('loaded');
});

// Mostrar contenido después de un tiempo máximo para evitar bloqueos
setTimeout(function() {
    if (!document.body.classList.contains('loaded')) {
        document.body.classList.add('loaded');
    }
}, 1000);
</script>
</html>
