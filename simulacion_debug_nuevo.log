[2025-07-13 17:57:26] Iniciando script registrar_salida_inventario.php
[2025-07-13 17:57:26] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 11:57:26"}
[2025-07-13 17:57:26] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 11:57:26
)

[2025-07-13 17:57:26] Es simulación: SÍ
[2025-07-13 17:57:26] Procesando simulación de venta
[2025-07-13 17:57:26] Productos recibidos: 1
[2025-07-13 17:57:26] Referencia: Simulación de venta - 13/7/2025, 11:57:26, Usuario ID: 1, Almacén ID: 1
[2025-07-13 17:57:26] Iniciando proceso de registro de salida de inventario
[2025-07-13 17:57:26] Conexión a base de datos establecida
[2025-07-13 17:57:26] Transacción iniciada
[2025-07-13 17:57:26] Iniciando procesamiento de productos
[2025-07-13 17:57:26] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 17:57:26] Producto ID válido: 56
[2025-07-13 17:57:26] Cantidad válida: 1
[2025-07-13 17:57:26] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 17:57:26] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 17:57:26] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 17:57:26] Iniciando actualización de lotes
[2025-07-13 17:57:26] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 17:57:26] Registrando movimiento de inventario
[2025-07-13 17:57:26] Movimiento de inventario registrado exitosamente
[2025-07-13 17:57:26] Actualizando stock en tabla stock
[2025-07-13 17:57:26] Valores para actualización: cantidadLote=1, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-13 17:57:26] Stock antes de actualización: 3
[2025-07-13 17:57:26] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-13 17:57:26] Stock después de actualización: 2
[2025-07-13 17:57:26] Diferencia calculada: 1 (debería ser 1)
[2025-07-13 17:57:26] Cantidad pendiente restante: 0
[2025-07-13 17:57:26] Producto procesado exitosamente
[2025-07-13 18:02:19] Iniciando script registrar_salida_inventario.php
[2025-07-13 18:02:19] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 12:02:19"}
[2025-07-13 18:02:19] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 12:02:19
)

[2025-07-13 18:02:19] Es simulación: SÍ
[2025-07-13 18:02:19] Procesando simulación de venta
[2025-07-13 18:02:19] Productos recibidos: 1
[2025-07-13 18:02:19] Referencia: Simulación de venta - 13/7/2025, 12:02:19, Usuario ID: 1, Almacén ID: 1
[2025-07-13 18:02:19] Iniciando proceso de registro de salida de inventario
[2025-07-13 18:02:20] Conexión a base de datos establecida
[2025-07-13 18:02:20] Transacción iniciada
[2025-07-13 18:02:20] Iniciando procesamiento de productos
[2025-07-13 18:02:20] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 18:02:20] Producto ID válido: 56
[2025-07-13 18:02:20] Cantidad válida: 1
[2025-07-13 18:02:20] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 18:02:20] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 18:02:20] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 18:02:20] Iniciando actualización de lotes
[2025-07-13 18:02:20] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 18:02:20] Registrando movimiento de inventario
[2025-07-13 18:02:20] Movimiento de inventario registrado exitosamente
[2025-07-13 18:02:20] Actualizando stock en tabla stock
[2025-07-13 18:02:20] Valores para actualización: cantidadLote=1, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-13 18:02:20] Stock antes de actualización: 3
[2025-07-13 18:02:20] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-13 18:02:21] Stock después de actualización: 2
[2025-07-13 18:02:21] Diferencia calculada: 1 (debería ser 1)
[2025-07-13 18:02:21] Cantidad pendiente restante: 0
[2025-07-13 18:02:21] Producto procesado exitosamente
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.0996] === INICIANDO SCRIPT registrar_salida_inventario.php ===
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.101] REQUEST_METHOD: POST
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1023] HTTP_USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1037] REMOTE_ADDR: ::1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1051] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 13:22:40"}
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1063] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 13:22:40
)

[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1076] Es simulación: SÍ
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1085] Procesando simulación de venta
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1096] Productos recibidos: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.1105] Referencia: Simulación de venta - 13/7/2025, 13:22:40, Usuario ID: 1, Almacén ID: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.112] Iniciando proceso de registro de salida de inventario
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.4048] Conexión a base de datos establecida
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.4553] Transacción iniciada
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.5072] Iniciando procesamiento de productos
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.509] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.5108] Producto ID válido: 56
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.5123] Cantidad válida: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.5141] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6234] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6253] Stock total disponible: 4, cantidad requerida: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6273] Iniciando actualización de lotes
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.629] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6311] Registrando movimiento de inventario
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6755] Movimiento de inventario registrado exitosamente
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6779] Actualizando stock en tabla stock
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.6799] Valores para actualización: cantidadLote=1, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.7755] Stock antes de actualización: 3
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.8633] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.9631] Stock después de actualización: 2
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.9656] Diferencia calculada: 1 (debería ser 1)
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.9681] Cantidad pendiente restante: 0
[2025-07-13 19:22:41] [PID:20188] [TIME:1752427361.9702] Producto procesado exitosamente
[2025-07-13 19:22:42] [PID:20188] [TIME:1752427362.0195] === SCRIPT COMPLETADO EXITOSAMENTE ===
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8693] === INICIANDO SCRIPT registrar_salida_inventario.php ===
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8711] REQUEST_METHOD: POST
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8725] HTTP_USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8739] REMOTE_ADDR: ::1
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8751] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":1,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 13:43:20"}
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8765] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 1
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 13:43:20
)

[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8779] Es simulación: SÍ
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8793] Procesando simulación de venta
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8811] Productos recibidos: 1
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8823] Referencia: Simulación de venta - 13/7/2025, 13:43:20, Usuario ID: 1, Almacén ID: 1
[2025-07-13 19:43:20] [PID:20188] [TIME:1752428600.8839] Iniciando proceso de registro de salida de inventario
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.1664] Conexión a base de datos establecida
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.214] Transacción iniciada
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2634] Iniciando procesamiento de productos
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2646] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 1
    [precio] => 27000
)

[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2657] Producto ID válido: 56
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2666] Cantidad válida: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.2675] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.3585] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 2
        )

)

[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.3593] Stock total disponible: 2, cantidad requerida: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.3601] Iniciando actualización de lotes
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.361] Procesando lote: LOT-20250522-0040, cantidad lote: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.362] Registrando movimiento de inventario
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.4324] Movimiento de inventario registrado exitosamente
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.4336] Actualizando stock en tabla stock
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.4348] Valores para actualización: cantidadLote=1, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.5286] Stock antes de actualización: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.6194] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7064] Stock después de actualización: 0
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7081] Diferencia calculada: 1 (debería ser 1)
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7097] Cantidad pendiente restante: 0
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7116] Producto procesado exitosamente
[2025-07-13 19:43:21] [PID:20188] [TIME:1752428601.7526] === SCRIPT COMPLETADO EXITOSAMENTE ===
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6024] === INICIANDO SCRIPT registrar_salida_inventario.php ===
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6031] REQUEST_METHOD: POST
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6037] HTTP_USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6044] REMOTE_ADDR: ::1
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6051] Input recibido - {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":2,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 18:26:57"}
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6057] Datos decodificados - Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 2
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 18:26:57
)

[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6065] Es simulación: SÍ
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6071] Procesando simulación de venta
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6078] Productos recibidos: 1
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.6084] Referencia: Simulación de venta - 13/7/2025, 18:26:57, Usuario ID: 1, Almacén ID: 1
[2025-07-14 00:26:57] [PID:17680] [TIME:1752445617.609] Iniciando proceso de registro de salida de inventario
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.0106] Conexión a base de datos establecida
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.0779] Transacción iniciada
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.1617] Iniciando procesamiento de productos
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.1622] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 2
    [precio] => 27000
)

[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.1627] Producto ID válido: 56
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.1631] Cantidad válida: 2
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.1635] Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.286] Lotes encontrados: 1 - Array
(
    [0] => Array
        (
            [lote] => LOT-20250522-0040
            [cantidad] => 4
        )

)

[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.2865] Stock total disponible: 4, cantidad requerida: 2
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.2869] Iniciando actualización de lotes
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.2874] Procesando lote: LOT-20250522-0040, cantidad lote: 2
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.2878] Registrando movimiento de inventario
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.3532] Movimiento de inventario registrado exitosamente
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.3537] Actualizando stock en tabla stock
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.3541] Valores para actualización: cantidadLote=2, repuesto_id=56, almacen_id=1, lote=LOT-20250522-0040
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.4974] Stock antes de actualización: 2
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.6622] Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.7979] Stock después de actualización: 0
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.7985] Diferencia calculada: 2 (debería ser 2)
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.7991] Cantidad pendiente restante: 0
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.7996] Producto procesado exitosamente
[2025-07-14 00:26:58] [PID:17680] [TIME:1752445618.8618] === SCRIPT COMPLETADO EXITOSAMENTE ===
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7693] 🚀 === INICIANDO SCRIPT registrar_salida_inventario.php ===
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7702] 🌐 REQUEST_METHOD: POST
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7707] 🌐 HTTP_USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7712] 🌐 REMOTE_ADDR: ::1
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7717] ⏰ TIMESTAMP: 2025-07-14 01:07:59
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7724] 🔢 PID: 17680
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7729] ✅ Método POST verificado correctamente
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7736] 📥 Obteniendo datos del cuerpo de la solicitud...
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7743] 📥 Input recibido (longitud: 202 caracteres)
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7749] 📥 Input crudo: {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":2,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 19:07:59"}
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7757] 🔄 Decodificando JSON...
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7763] 🔄 Datos decodificados correctamente
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7769] 📋 DATOS COMPLETOS RECIBIDOS:
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7776] Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 2
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 19:07:59
)

[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7781] Es simulación: SÍ
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7785] Procesando simulación de venta
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.779] Productos recibidos: 1
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7794] Referencia: Simulación de venta - 13/7/2025, 19:07:59, Usuario ID: 1, Almacén ID: 1
[2025-07-14 01:07:59] [PID:17680] [TIME:1752448079.7798] Iniciando proceso de registro de salida de inventario
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.0606] Conexión a base de datos establecida
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.1073] Transacción iniciada
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.1555] Iniciando procesamiento de productos
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.1561] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 2
    [precio] => 27000
)

[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.1566] ✅ Producto ID válido: 56
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.1572] ✅ Cantidad válida: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.1578] 🔍 === VERIFICACIÓN STOCK INICIAL ===
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.2512] 📊 VERIFICACIÓN STOCK - Repuesto ID: 56, Almacén ID: 1
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.2517] 📊 LOTE: LOT-20250522-0040 - CANTIDAD: 4
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.2522] 📊 STOCK TOTAL: 4 unidades
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.2527] 🔍 Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3425] 📦 Lotes encontrados: 1
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3431] 📦 Lote 1: LOT-20250522-0040 - Cantidad: 4
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3434] 📊 Stock total disponible: 4 unidades
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3438] 📊 Cantidad requerida: 2 unidades
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3442] 📊 Diferencia: 2 unidades
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3446] ✅ Stock suficiente verificado
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.345] 🔄 Cantidad pendiente inicial: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3454] 🔄 === INICIANDO ACTUALIZACIÓN DE LOTES (FIFO) ===
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3457] 🔄 === PROCESANDO LOTE 1 ===
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3461] 🔄 Lote: LOT-20250522-0040
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3465] 🔄 Cantidad disponible en lote: 4
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3468] 🔄 Cantidad pendiente: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3472] 🔄 Cantidad a descontar de este lote: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3476] 📝 === REGISTRANDO MOVIMIENTO DE INVENTARIO ===
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.348] 📝 Repuesto ID: 56
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3483] 📝 Cantidad: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3487] 📝 Usuario: admin
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3491] 📝 Referencia: Simulación de venta - 13/7/2025, 19:07:59
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.3495] 📝 Almacén ID: 1
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4027] ✅ Movimiento de inventario registrado exitosamente (ID: 191)
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4036] 📊 === ACTUALIZANDO STOCK EN TABLA STOCK ===
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4044] 📊 Parámetros de actualización:
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4052] 📊   - Cantidad a descontar: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.406] 📊   - Repuesto ID: 56
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4067] 📊   - Almacén ID: 1
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4075] 📊   - Lote: LOT-20250522-0040
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4083] 🔍 Verificando stock ANTES de la actualización...
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.495] 🔍 Stock ANTES de actualización: 2 unidades
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4957] 💾 Preparando query UPDATE...
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.4962] 💾 SQL: UPDATE stock SET cantidad = cantidad - 2 WHERE repuesto_id = 56 AND almacen_id = 1 AND lote = 'LOT-20250522-0040'
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.5469] 💾 Ejecutando UPDATE...
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.5948] ✅ Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.5954] 🔍 Verificando stock DESPUÉS de la actualización...
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.699] 🔍 Stock DESPUÉS de actualización: 0 unidades
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.6995] 📊 ANÁLISIS DE ACTUALIZACIÓN:
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7] 📊   - Stock antes: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7005] 📊   - Stock después: 0
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7009] 📊   - Diferencia real: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7013] 📊   - Diferencia esperada: 2
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7017] 📊   - ¿Coincide? ✅ SÍ
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.702] 🔄 Cantidad pendiente restante: 0
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7024] ✅ Cantidad pendiente agotada, terminando procesamiento de lotes
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7028] 🔍 === VERIFICACIÓN STOCK FINAL ===
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7988] 📊 VERIFICACIÓN STOCK - Repuesto ID: 56, Almacén ID: 1
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.7995] 📊 LOTE: LOT-20250522-0040 - CANTIDAD: 0
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.8001] 📊 STOCK TOTAL: 0 unidades
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.8007] ✅ Producto procesado exitosamente
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.8012] 💾 === CONFIRMANDO TRANSACCIÓN ===
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.8512] ✅ Transacción confirmada exitosamente
[2025-07-14 01:08:00] [PID:17680] [TIME:1752448080.8517] === SCRIPT COMPLETADO EXITOSAMENTE ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1571] 🚀 === INICIANDO SCRIPT registrar_salida_inventario.php ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1576] 🌐 REQUEST_METHOD: POST
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1582] 🌐 HTTP_USER_AGENT: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1587] 🌐 REMOTE_ADDR: ::1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1594] ⏰ TIMESTAMP: 2025-07-14 01:22:21
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.16] 🔢 PID: 17680
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1608] ✅ Método POST verificado correctamente
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1616] 📥 Obteniendo datos del cuerpo de la solicitud...
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1624] 📥 Input recibido (longitud: 202 caracteres)
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1633] 📥 Input crudo: {"simulacion":true,"productos":[{"repuesto_id":"56","nombre":"Valvulas Escape 4U","cantidad":2,"precio":27000}],"usuario_id":1,"almacen_id":"1","referencia":"Simulación de venta - 13/7/2025, 19:22:21"}
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.164] 🔄 Decodificando JSON...
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1645] 🔄 Datos decodificados correctamente
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1651] 📋 DATOS COMPLETOS RECIBIDOS:
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1658] Array
(
    [simulacion] => 1
    [productos] => Array
        (
            [0] => Array
                (
                    [repuesto_id] => 56
                    [nombre] => Valvulas Escape 4U
                    [cantidad] => 2
                    [precio] => 27000
                )

        )

    [usuario_id] => 1
    [almacen_id] => 1
    [referencia] => Simulación de venta - 13/7/2025, 19:22:21
)

[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1705] Es simulación: SÍ
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1712] Procesando simulación de venta
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1719] Productos recibidos: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.1724] Referencia: Simulación de venta - 13/7/2025, 19:22:21, Usuario ID: 1, Almacén ID: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.173] Iniciando proceso de registro de salida de inventario
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.3563] Conexión a base de datos establecida
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.3841] Transacción iniciada
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.4157] Iniciando procesamiento de productos
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.4163] Procesando producto 0: Array
(
    [repuesto_id] => 56
    [nombre] => Valvulas Escape 4U
    [cantidad] => 2
    [precio] => 27000
)

[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.4168] ✅ Producto ID válido: 56
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.4173] ✅ Cantidad válida: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.4178] 🔍 === VERIFICACIÓN STOCK INICIAL ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.4874] 📊 VERIFICACIÓN STOCK - Repuesto ID: 56, Almacén ID: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.488] 📊 LOTE: LOT-20250522-0040 - CANTIDAD: 4
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.4885] 📊 STOCK TOTAL: 4 unidades
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.4891] 🔍 Consultando lotes para repuesto_id: 56, almacen_id: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5555] 📦 Lotes encontrados: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5562] 📦 Lote 1: LOT-20250522-0040 - Cantidad: 4
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5569] 📊 Stock total disponible: 4 unidades
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5575] 📊 Cantidad requerida: 2 unidades
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5582] 📊 Diferencia: 2 unidades
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5588] ✅ Stock suficiente verificado
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5593] 🔄 Cantidad pendiente inicial: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5599] 🔄 === INICIANDO ACTUALIZACIÓN DE LOTES (FIFO) ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5605] 🔄 === PROCESANDO LOTE 1 ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.561] 🔄 Lote: LOT-20250522-0040
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5615] 🔄 Cantidad disponible en lote: 4
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.562] 🔄 Cantidad pendiente: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5627] 🔄 Cantidad a descontar de este lote: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5633] 📝 === REGISTRANDO MOVIMIENTO DE INVENTARIO ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5638] 📝 Repuesto ID: 56
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5643] 📝 Cantidad: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5648] 📝 Usuario: admin
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5654] 📝 Referencia: Simulación de venta - 13/7/2025, 19:22:21
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.5659] 📝 Almacén ID: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6004] ✅ Movimiento de inventario registrado exitosamente (ID: 192)
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6011] 📊 === ACTUALIZANDO STOCK EN TABLA STOCK ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6017] 📊 Parámetros de actualización:
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6023] 📊   - Cantidad a descontar: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6029] 📊   - Repuesto ID: 56
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6035] 📊   - Almacén ID: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6042] 📊   - Lote: LOT-20250522-0040
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6047] 🔍 Verificando stock ANTES de la actualización...
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6799] 🔍 Stock ANTES de actualización: 4 unidades
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6807] 💾 Preparando query UPDATE...
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.6813] 💾 SQL: UPDATE stock SET cantidad = cantidad - 2 WHERE repuesto_id = 56 AND almacen_id = 1 AND lote = 'LOT-20250522-0040'
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.7185] 💾 Ejecutando UPDATE...
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.7513] ✅ Stock actualizado exitosamente. Filas afectadas: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.7519] 🔍 Verificando stock DESPUÉS de la actualización...
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8074] 🔍 Stock DESPUÉS de actualización: 2 unidades
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.808] 📊 ANÁLISIS DE ACTUALIZACIÓN:
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8086] 📊   - Stock antes: 4
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8091] 📊   - Stock después: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8097] 📊   - Diferencia real: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8102] 📊   - Diferencia esperada: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8106] 📊   - ¿Coincide? ✅ SÍ
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8111] 🔄 Cantidad pendiente restante: 0
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8116] ✅ Cantidad pendiente agotada, terminando procesamiento de lotes
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8123] 🔍 === VERIFICACIÓN STOCK FINAL ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8679] 📊 VERIFICACIÓN STOCK - Repuesto ID: 56, Almacén ID: 1
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8684] 📊 LOTE: LOT-20250522-0040 - CANTIDAD: 2
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8689] 📊 STOCK TOTAL: 2 unidades
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8698] ✅ Producto procesado exitosamente
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.8704] 💾 === CONFIRMANDO TRANSACCIÓN ===
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.9074] ✅ Transacción confirmada exitosamente
[2025-07-14 01:22:21] [PID:17680] [TIME:1752448941.9081] === SCRIPT COMPLETADO EXITOSAMENTE ===
