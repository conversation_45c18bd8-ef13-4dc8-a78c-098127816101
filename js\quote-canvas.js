// Variables globales para el canvas de cotización
let quoteItems = [];
let currentQuoteId = 0;
let currentQuoteLevel = 1; // 1: formulario, 2: lista, 3: detalle

// Función global para navegar entre niveles
function navigateToLevel(level) {
    const slideContainer = document.querySelector('.quote-slide-container');
    const slideInner = document.querySelector('.quote-slide-inner');
    const backQuoteBtn = document.getElementById('backQuoteBtn');
    const quoteTitle = document.getElementById('quoteTitle');

    if (!slideContainer || !slideInner || !backQuoteBtn || !quoteTitle) {
        console.error('Elementos necesarios para la navegación no encontrados');
        return;
    }

    console.log('Navegando al nivel:', level);
    currentQuoteLevel = level;
    let activeLevel;

    switch(level) {
        case 1:
            slideInner.classList.remove('show-list', 'show-detail');
            backQuoteBtn.style.display = 'none';
            quoteTitle.innerHTML = '<i class="fas fa-file-invoice-dollar"></i> Generar Cotización';
            activeLevel = document.getElementById('quoteFormLevel');
            break;
        case 2:
            slideInner.classList.add('show-list');
            slideInner.classList.remove('show-detail');
            backQuoteBtn.style.display = 'inline-block';
            quoteTitle.innerHTML = '<i class="fas fa-clipboard-list"></i> Lista de Cotizaciones';
            activeLevel = document.getElementById('quoteListLevel');
            break;
        case 3:
            slideInner.classList.add('show-detail');
            backQuoteBtn.style.display = 'inline-block';
            quoteTitle.innerHTML = '<i class="fas fa-file-invoice"></i> Detalle de Cotización';
            activeLevel = document.getElementById('quoteDetailLevel');
            break;
    }

    // Ajustar la altura del contenedor dinámicamente
    if (activeLevel) {
        // Usamos un pequeño delay para que el DOM se actualice antes de medir la altura
        setTimeout(() => {
            const activeLevelHeight = activeLevel.scrollHeight;
            // Solo ajustamos si la altura es mayor a cero
            if (activeLevelHeight > 0) {
                slideContainer.style.height = `${activeLevelHeight}px`;
            }
        }, 100); // Aumentamos el delay para asegurar la renderización
    }
}
    


// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado - Inicializando canvas de cotización');
    
    // Elementos del DOM
    const quoteCanvas = document.getElementById('quoteCanvas');
    const quoteOverlay = document.getElementById('quoteOverlay');
    const openQuoteBtn = document.getElementById('openQuoteBtn');
    const closeQuoteBtn = document.getElementById('closeQuoteBtn');
    const quoteItemsContainer = document.getElementById('quoteItemsContainer');
    const quoteTotals = document.getElementById('quoteTotals');
    const slideInner = document.querySelector('.quote-slide-inner');
    const backQuoteBtn = document.getElementById('backQuoteBtn');
    const quoteTitle = document.getElementById('quoteTitle');

    // Manejo de apertura y cierre del canvas
    if (openQuoteBtn) {
        openQuoteBtn.addEventListener('click', () => {
            console.log('Botón de cotización clickeado');
            if (quoteCanvas && quoteOverlay) {
                quoteCanvas.classList.add('active');
                quoteOverlay.classList.add('active');
                console.log('Canvas de cotización activado');
            } else {
                console.error('Canvas o overlay no encontrado', {quoteCanvas, quoteOverlay});
            }
        });
    } else {
        console.warn('Botón openQuoteBtn no encontrado en el DOM');
    }

    if (closeQuoteBtn) {
        closeQuoteBtn.addEventListener('click', () => {
            closeQuoteCanvas();
        });
    }

    if (quoteOverlay) {
        quoteOverlay.addEventListener('click', () => {
            closeQuoteCanvas();
        });
    }

    // Botón de volver
    if (backQuoteBtn) {
        backQuoteBtn.addEventListener('click', () => {
            navigateToLevel(currentQuoteLevel - 1);
        });
    }

    // Función para cerrar el canvas
    function closeQuoteCanvas() {
        quoteCanvas.classList.remove('active');
        quoteOverlay.classList.remove('active');
        // Volver al primer nivel cuando se cierra
        setTimeout(() => {
            navigateToLevel(1);
        }, 300);
    }

    // Manejo de tabs
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    console.log('Tab Buttons encontrados:', tabButtons.length);
    console.log('Tab Contents encontrados:', tabContents.length);

    if (tabButtons.length > 0 && tabContents.length > 0) {
        // Activar la primera pestaña por defecto
        tabButtons[0].classList.add('active');
        tabContents[0].classList.add('active');
    }

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tab = button.getAttribute('data-tab');
            
            // Remover clase active de todos los botones y contenidos
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Agregar clase active al botón y contenido seleccionado
            button.classList.add('active');
            const activeContent = document.getElementById(`${tab}-tab`);
            if (activeContent) {
                activeContent.classList.add('active');
            } else {
                console.error(`Contenido de tab no encontrado para: ${tab}-tab`);
            }
        });
    });

    // Búsqueda de productos en tiempo real
    const searchInput = document.getElementById('quoteProductSearch');
    const searchResults = document.getElementById('quoteSearchResults');

    if (searchInput && searchResults) {
        searchInput.addEventListener('input', debounce(function() {
            const searchTerm = this.value ? this.value.trim() : '';
            
            if (searchTerm.length < 2) {
                searchResults.innerHTML = '';
                return;
            }
            
            searchProducts(searchTerm);
        }, 300));
    }

    // Hacer las funciones globales para que puedan ser llamadas desde onclick
    window.addManualProduct = addManualProduct;
    window.clearQuote = clearQuote;
    window.saveQuote = saveQuote;
    window.generateQuote = generateQuote;
    window.openQuotesList = openQuotesList;
    window.removeQuoteItem = removeQuoteItem;
    window.updateQuoteItemQuantity = updateQuoteItemQuantity;
    window.addToQuote = addToQuote;
    window.navigateToLevel = navigateToLevel;
    window.closeQuoteCanvas = closeQuoteCanvas;
    window.addProductFromSearch = addProductFromSearch;
    
    // Inicializar contador de cotización
    updateQuoteCount();
});

// Función para agregar producto manualmente
function addManualProduct() {
    const name = document.getElementById('manualProductName').value.trim();
    const price = parseFloat(document.getElementById('manualProductPrice').value) || 0;
    const quantity = parseInt(document.getElementById('manualProductQuantity').value) || 1;
    
    if (!name) {
        alert('Por favor ingrese el nombre del producto');
        return;
    }
    
    if (price <= 0) {
        alert('Por favor ingrese un precio válido');
        return;
    }
    
    // Crear un ID único para el producto manual
    const manualId = 'manual_' + Date.now();
    
    addItemToQuote({
        id: manualId,
        name: name,
        price: price,
        quantity: quantity,
        isManual: true
    });
    
    // Limpiar los campos
    document.getElementById('manualProductName').value = '';
    document.getElementById('manualProductPrice').value = '';
    document.getElementById('manualProductQuantity').value = '1';
}

// Función para buscar productos
function searchProducts(term) {
    const searchResults = document.getElementById('quoteSearchResults');
    if (!searchResults) {
        console.error('Elemento searchResults no encontrado');
        return;
    }
    
    searchResults.innerHTML = '<div class="loading">Buscando productos...</div>';
    
    fetch(`search_products.php?term=${encodeURIComponent(term)}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.data && data.data.length > 0) {
                displaySearchResults(data.data);
            } else if (data.products && data.products.length > 0) {
                // Fallback para formato antiguo
                displaySearchResults(data.products);
            } else {
                searchResults.innerHTML = '<div class="no-results">No se encontraron productos</div>';
            }
        })
        .catch(error => {
            console.error('Error buscando productos:', error);
            searchResults.innerHTML = '<div class="error">Error al buscar productos</div>';
        });
}

// Función para mostrar resultados de búsqueda
function displaySearchResults(products) {
    const searchResults = document.getElementById('quoteSearchResults');
    
    const resultsHTML = products.map(product => `
        <div class="search-result-item">
            <div class="product-info">
                <h4>${product.nombre}</h4>
                <p>SKU: ${product.sku} | Stock: ${product.stock_total || 0}</p>
                <p class="price">$${Number(product.precio_venta).toLocaleString('es-CL')}</p>
            </div>
            <button class="action-button small" onclick="addProductFromSearch('${product.id}', '${product.nombre.replace(/'/g, "\\'")}', ${product.precio_venta})">
                <i class="fas fa-plus"></i>
            </button>
        </div>
    `).join('');
    
    searchResults.innerHTML = resultsHTML;
}

// Función para agregar producto desde búsqueda
function addProductFromSearch(id, name, price) {
    addItemToQuote({
        id: id,
        name: name,
        price: price,
        quantity: 1,
        isManual: false
    });
}

// Función para agregar item a la cotización
function addItemToQuote(item) {
    // Verificar si el producto ya existe
    const existingItem = quoteItems.find(i => i.id === item.id);
    
    if (existingItem) {
        existingItem.quantity += item.quantity;
    } else {
        quoteItems.push(item);
    }
    
    updateQuoteCount();
    renderQuoteItems();
}

// Función para renderizar los items de la cotización
function renderQuoteItems() {
    const container = document.getElementById('quoteItemsContainer');
    const totals = document.getElementById('quoteTotals');
    
    if (quoteItems.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <p>No hay productos agregados a la cotización</p>
            </div>
        `;
        totals.style.display = 'none';
        return;
    }
    
    const itemsHTML = quoteItems.map((item, index) => `
        <div class="quote-item" data-index="${index}">
            <div class="item-info">
                <h4>${item.name}</h4>
                <p class="item-price">$${Number(item.price).toLocaleString('es-CL')} c/u</p>
            </div>
            <div class="item-controls">
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuoteItemQuantity(${index}, -1)">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuoteItemQuantity(${index}, 1)">+</button>
                </div>
                <div class="item-total">
                    $${(item.price * item.quantity).toLocaleString('es-CL')}
                </div>
                <button class="remove-btn" onclick="removeQuoteItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = itemsHTML;
    totals.style.display = 'block';
    
    // Calcular y mostrar totales
    updateTotals();
}

// Función para actualizar cantidad de un item
function updateQuoteItemQuantity(index, change) {
    const item = quoteItems[index];
    item.quantity = Math.max(1, item.quantity + change);
    renderQuoteItems();
    updateQuoteCount();
}

// Función para eliminar un item
function removeQuoteItem(index) {
    quoteItems.splice(index, 1);
    renderQuoteItems();
    updateQuoteCount();
}

// Función para actualizar totales
function updateTotals() {
    const subtotal = quoteItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    document.getElementById('quoteSubtotal').textContent = `$${subtotal.toLocaleString('es-CL')}`;
    document.getElementById('quoteTotal').textContent = `$${subtotal.toLocaleString('es-CL')}`;
}

// Función para actualizar contador en el botón
function updateQuoteCount() {
    const countElement = document.querySelector('.quote-count');
    if (countElement) {
        const totalItems = quoteItems.reduce((sum, item) => sum + item.quantity, 0);
        countElement.textContent = totalItems;
        
        // Mostrar/ocultar contador según si hay elementos
        if (totalItems > 0) {
            countElement.style.visibility = 'visible';
        } else {
            countElement.style.visibility = 'hidden';
        }
        
        // Añadir animación
        countElement.classList.add('updated');
        setTimeout(() => {
            countElement.classList.remove('updated');
        }, 300);
    }
}

// Función para limpiar la cotización
function clearQuote() {
    if (quoteItems.length === 0) return;
    
    if (confirm('¿Está seguro de que desea limpiar toda la cotización?')) {
        quoteItems = [];
        renderQuoteItems();
        updateQuoteCount();
        
        // Limpiar campos del formulario
        document.getElementById('quoteClientName').value = '';
        document.getElementById('quoteClientRut').value = '';
        document.getElementById('quoteClientEmail').value = '';
        document.getElementById('quoteClientPhone').value = '';
        document.getElementById('quoteNotes').value = '';
    }
}

// Función para guardar la cotización
function saveQuote() {
    if (quoteItems.length === 0) {
        alert('No hay productos en la cotización');
        return;
    }
    
    const clientName = document.getElementById('quoteClientName').value.trim();
    if (!clientName) {
        alert('Por favor ingrese el nombre del cliente');
        return;
    }
    
    const quoteData = {
        client: {
            name: clientName,
            rut: document.getElementById('quoteClientRut').value.trim(),
            email: document.getElementById('quoteClientEmail').value.trim(),
            phone: document.getElementById('quoteClientPhone').value.trim()
        },
        items: quoteItems,
        notes: document.getElementById('quoteNotes').value.trim(),
        total: quoteItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    };
    
    // Mostrar indicador de carga
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
    
    fetch('save_quote.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(quoteData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Cotización guardada exitosamente');
            clearQuote();
        } else {
            alert('Error al guardar la cotización: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al guardar la cotización');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-floppy-disk"></i> Guardar';
    });
}

// Función para generar PDF
function generateQuote() {
    if (quoteItems.length === 0) {
        alert('No hay productos en la cotización');
        return;
    }
    
    const clientName = document.getElementById('quoteClientName').value.trim();
    if (!clientName) {
        alert('Por favor ingrese el nombre del cliente');
        return;
    }
    
    const quoteData = {
        client: {
            name: clientName,
            rut: document.getElementById('quoteClientRut').value.trim(),
            email: document.getElementById('quoteClientEmail').value.trim(),
            phone: document.getElementById('quoteClientPhone').value.trim()
        },
        items: quoteItems,
        notes: document.getElementById('quoteNotes').value.trim(),
        total: quoteItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    };
    
    // Mostrar indicador de carga
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generando PDF...';
    
    fetch('generate_quote_pdf.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(quoteData)
    })
    .then(response => response.blob())
    .then(blob => {
        // Descargar el PDF
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cotizacion_${Date.now()}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
        
        alert('PDF generado exitosamente');
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al generar el PDF');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-file-export"></i> Generar PDF';
    });
}

// Función para abrir lista de cotizaciones
function openQuotesList() {
    // Navegar al nivel 2 (lista)
    navigateToLevel(2);
    
    // Cargar las cotizaciones
    loadQuotesList();
}

// Función para agregar a cotización desde otros lugares
function addToQuote(productId, productName, productPrice) {
    console.log('Agregando a cotización:', productId, productName, productPrice);
    
    try {
        addItemToQuote({
            id: productId,
            name: productName,
            price: parseFloat(productPrice),
            quantity: 1,
            isManual: false
        });
        
        console.log('Producto agregado exitosamente a cotización');
        
        // Mostrar notificación
        const notification = document.createElement('div');
        notification.className = 'quote-notification';
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>Producto agregado a la cotización</span>
        `;
        document.body.appendChild(notification);
        
        // Animar la notificación
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 2000);
        
    } catch (error) {
        console.error('Error al agregar producto a cotización:', error);
        alert('Error al agregar producto a la cotización');
    }
}

// Función debounce para optimizar búsquedas
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Función para cargar lista de cotizaciones
function loadQuotesList() {
    console.log('Cargando lista de cotizaciones...');
    const container = document.getElementById('quotesListContainer');
    
    if (!container) {
        console.error('Container de lista de cotizaciones no encontrado');
        return;
    }
    
    container.innerHTML = '<div class="loading-quotes"><i class="fas fa-spinner fa-spin"></i> Cargando cotizaciones...</div>';
    
    fetch('get_quotes.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.quotes && data.quotes.length > 0) {
                displayQuotesList(data.quotes);
            } else {
                container.innerHTML = `
                    <div class="no-quotes">
                        <i class="fas fa-file-invoice"></i>
                        <p>No hay cotizaciones guardadas</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error cargando cotizaciones:', error);
            container.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error al cargar las cotizaciones</p>
                </div>
            `;
        });
}

// Función para mostrar la lista de cotizaciones
function displayQuotesList(quotes) {
    const container = document.getElementById('quotesListContainer');
    
    const quotesHTML = quotes.map(quote => `
        <div class="quote-list-item" data-id="${quote.id}" onclick="viewQuoteDetail(${quote.id})" style="cursor: pointer;">
            <div class="quote-info">
                <h4>Cotización #${quote.numero || quote.id}</h4>
                <p class="client-name">${quote.cliente_nombre || 'Sin cliente'}</p>
                <p class="quote-date">${formatDate(quote.fecha_creacion)}</p>
            </div>
            <div class="quote-amount">
                <p class="total">$${Number(quote.total || 0).toLocaleString('es-CL')}</p>
                <p class="items-count">${quote.cantidad_items || 0} items</p>
            </div>
            <div class="quote-actions" onclick="event.stopPropagation();">
                <button class="action-btn pdf" onclick="downloadQuotePDF(${quote.id})" title="Descargar PDF">
                    <i class="fas fa-file-pdf"></i>
                </button>
                <button class="action-btn delete" onclick="deleteQuote(${quote.id})" title="Eliminar">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = quotesHTML;
}

// Función para formatear fecha
function formatDate(dateString) {
    if (!dateString) return 'Sin fecha';
    const date = new Date(dateString);
    return date.toLocaleDateString('es-CL', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Función para ver detalle de cotización
function viewQuoteDetail(quoteId) {
    console.log('Ver detalle de cotización:', quoteId);
    
    // Validar que el ID sea válido
    if (!quoteId || isNaN(quoteId)) {
        console.error('ID de cotización inválido:', quoteId);
        alert('Error: ID de cotización inválido');
        return;
    }
    
    // Primero cargar los datos y luego navegar al nivel 3
    try {
        // Asegurarse de que el contenedor de detalle exista
        const container = document.getElementById('quoteDetailContent');
        if (!container) {
            console.error('Container de detalle no encontrado');
            alert('Error: Container de detalle no encontrado en el DOM');
            return;
        }
        
        // Mostrar indicador de carga
        container.innerHTML = '<div class="loading-detail"><i class="fas fa-spinner fa-spin"></i> Cargando detalle...</div>';
        
        // Navegar al nivel de detalle
        navigateToLevel(3);
        
        // Cargar el detalle
        loadQuoteDetail(quoteId);
    } catch (error) {
        console.error('Error al cargar detalle de cotización:', error);
        alert('Error al cargar el detalle de la cotización: ' + error.message);
    }
}

// Función para descargar PDF de cotización existente
function downloadQuotePDF(quoteId) {
    window.location.href = `download_quote_pdf.php?id=${quoteId}`;
}

// Función para eliminar cotización
function deleteQuote(quoteId) {
    if (!confirm('¿Está seguro de eliminar esta cotización?')) return;
    
    fetch(`delete_quote.php?id=${quoteId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadQuotesList(); // Recargar lista
        } else {
            alert('Error al eliminar la cotización');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al eliminar la cotización');
    });
}

// Función para cargar detalle de cotización
function loadQuoteDetail(quoteId) {
    console.log('Cargando detalle de cotización ID:', quoteId);
    const container = document.getElementById('quoteDetailContent');
    if (!container) {
        console.error('Container de detalle no encontrado');
        return;
    }

    // Mostrar indicador de carga mientras se obtienen los datos
    container.innerHTML = '<div class="loading-detail"><i class="fas fa-spinner fa-spin"></i> Cargando detalle...</div>';

    fetch(`get_quote_detail.php?id=${quoteId}`)
        .then(response => {
            console.log('Respuesta recibida:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Datos recibidos del servidor:', data);
            
            if (data.status === 'success' && data.quote) {
                displayQuoteDetail(data); // Pasamos todo el objeto data
            } else {
                console.error('Error en datos recibidos:', data);
                const errorMessage = data.message || 'Error desconocido al cargar el detalle';
                container.innerHTML = `<div class="error-message modern-card">Error: ${errorMessage}</div>`;
            }
        })
        .catch(error => {
            console.error('Error en fetch:', error);
            container.innerHTML = `<div class="error-message modern-card">Error de conexión: ${error.message}</div>`;
        });
}

function displayQuoteDetail(data) {
    console.log('Mostrando detalle de cotización con nuevo diseño:', data);
    const detailContent = document.getElementById('quoteDetailContent');

    if (!detailContent) {
        console.error('Contenedor de detalle de cotización no encontrado.');
        return;
    }

    // Validar la estructura de datos y manejar flexiblemente los campos
    if (!data) {
        detailContent.innerHTML = `<div class="error-message modern-card">No se pudo cargar el detalle. Datos no recibidos.</div>`;
        return;
    }
    
    // Extraer la cotización desde data.quote
    const quote = data.quote || {};
    
    console.log('Quote data:', quote);
    
    if (Object.keys(quote).length === 0) {
        detailContent.innerHTML = `<div class="error-message modern-card">Datos de cotización incompletos o vacíos.</div>`;
        return;
    }
    
    // Extraer productos desde quote.items o data.products (manejar ambas estructuras)
    const products = quote.items || data.products || [];
    console.log('Products data:', products);
    
    if (products.length === 0) {
        console.warn('No se encontraron productos en la cotización');
        // Continuamos aunque no haya productos
    }
    
    // Extraer ID y manejar diferentes formatos posibles
    const quoteId = quote.id || quote.id_cotizacion || '';
    
    // Formatear la fecha con manejo de error
    let formattedDate = 'Fecha no disponible';
    try {
        // El PHP devuelve fecha ya formateada como "DD-MM-YYYY HH:MM"
        if (quote.fecha) {
            formattedDate = quote.fecha; // Usar directamente la fecha formateada del servidor
        } else if (quote.fecha_cotizacion) {
            formattedDate = formatDate(quote.fecha_cotizacion);
        }
    } catch (error) {
        console.error('Error al formatear fecha:', error);
    }

    // --- Card de Información General ---
    const infoCardHtml = `
        <div class="modern-card info-card">
            <div class="card-header">
                <h2>Cotización #${String(quoteId).padStart(5, '0')}</h2>
                <span class="quote-status ${quote.estado || 'borrador'}">${quote.estado || 'Borrador'}</span>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div>
                        <i class="fas fa-user-tie"></i>
                        <strong>Cliente:</strong>
                        <p>${quote.cliente_nombre || quote.nombre_cliente || quote.cliente || 'Sin información'}</p>
                    </div>
                    <div>
                        <i class="fas fa-envelope"></i>
                        <strong>Email:</strong>
                        <p>${quote.cliente_email || quote.email_cliente || quote.email || 'No especificado'}</p>
                    </div>
                    <div>
                        <i class="fas fa-phone"></i>
                        <strong>Teléfono:</strong>
                        <p>${quote.cliente_telefono || quote.telefono_cliente || quote.telefono || 'No especificado'}</p>
                    </div>
                    <div>
                        <i class="fas fa-id-card"></i>
                        <strong>RUT:</strong>
                        <p>${quote.cliente_rut || 'No especificado'}</p>
                    </div>
                    <div>
                        <i class="fas fa-calendar-alt"></i>
                        <strong>Fecha:</strong>
                        <p>${formattedDate}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // --- Card de Productos ---
    const productsHtml = products.map(p => {
        // Extraer valores con opciones de respaldo
        const price = parseFloat(p.precio_unitario || p.precio || 0);
        const quantity = parseInt(p.cantidad || 1, 10);
        const total = price * quantity;
        
        // Manejar la imagen del producto
        let productImage = '';
        if (p.imagen && p.imagen !== 'null' && p.imagen !== '') {
            // Verificar si la ruta de la imagen ya contiene la ruta base
            const imagePath = p.imagen.startsWith('/') || p.imagen.includes('http') 
                ? p.imagen 
                : `../images/productos/${p.imagen}`;
            productImage = `<img src="${imagePath}" alt="${p.nombre || 'Producto'}" class="product-thumbnail" onerror="this.src='../images/no-image.png'; this.onerror=null;">`;
        } else {
            // Imagen por defecto si no hay imagen disponible
            productImage = `<img src="../images/no-image.png" alt="Sin imagen" class="product-thumbnail">`;
        }
        
        return `
            <tr>
                <td class="product-cell">
                    <div class="product-info-container">
                        <div class="product-image-container">
                            ${productImage}
                        </div>
                        <div class="product-details">
                            <span class="product-name">${p.nombre || p.nombre_producto || p.descripcion || 'Producto sin nombre'}</span>
                            <span class="product-sku">SKU: ${p.sku || p.codigo || 'N/A'}</span>
                        </div>
                    </div>
                </td>
                <td class="text-center">${quantity}</td>
                <td class="text-right">$${price.toLocaleString('es-CL')}</td>
                <td class="text-right font-bold">$${total.toLocaleString('es-CL')}</td>
            </tr>
        `;
    }).join('');

    const productsCardHtml = `
        <div class="modern-card products-card">
            <div class="card-header">
                <h3><i class="fas fa-box-open"></i> Productos</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th>Descripción</th>
                                <th class="text-center">Cant</th>
                                <th class="text-right">P. Unit</th>
                                <th class="text-right">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${productsHtml || '<tr><td colspan="4" class="text-center">No hay productos</td></tr>'}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    // --- Card de Totales y Notas ---
    // Usar valores calculados por el servidor si están disponibles
    let subtotal, iva, total;
    
    if (quote.subtotal !== undefined && quote.iva !== undefined && quote.total !== undefined) {
        // Usar valores del servidor
        subtotal = parseFloat(quote.subtotal);
        iva = parseFloat(quote.iva);
        total = parseFloat(quote.total);
        console.log('Usando totales del servidor:', { subtotal, iva, total });
    } else {
        // Calcular totales a partir de los productos
        subtotal = products.reduce((acc, p) => {
            const price = parseFloat(p.precio_unitario || p.precio || 0);
            const quantity = parseInt(p.cantidad || 1, 10);
            return acc + (price * quantity);
        }, 0);
        
        iva = subtotal * 0.16; 
        total = subtotal + iva;
        console.log('Totales calculados manualmente:', { subtotal, iva, total });
    }

    const summaryCardHtml = `
        <div class="modern-card summary-card">
             <div class="card-header">
                <h3><i class="fas fa-calculator"></i> Resumen y Notas</h3>
            </div>
            <div class="card-body">
                <div class="summary-grid">
                    <div class="notes-section">
                        <h4>Notas Adicionales:</h4>
                        <p>${quote.notas || quote.observaciones || 'No se agregaron notas.'}</p>
                    </div>
                    <div class="totals-section">
                        <div class="total-row">
                            <span>Subtotal:</span>
                            <span>$${subtotal.toLocaleString('es-CL')}</span>
                        </div>
                        <div class="total-row">
                            <span>IVA (16%):</span>
                            <span>$${iva.toLocaleString('es-CL')}</span>
                        </div>
                        <div class="total-row final-total">
                            <span>Total:</span>
                            <span>$${total.toLocaleString('es-CL')}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // --- Botones de Acción ---
    const actionsHtml = `
        <div class="detail-actions">
            <button class="action-button secondary" onclick="navigateToLevel(2)"><i class="fas fa-arrow-left"></i> Volver a la Lista</button>
            <button class="action-button primary" onclick="downloadQuotePDF(${quoteId})"><i class="fas fa-download"></i> Descargar PDF</button>
        </div>
    `;

    detailContent.innerHTML = `
        <div class="quote-detail-layout">
            ${infoCardHtml}
            ${productsCardHtml}
            ${summaryCardHtml}
            ${actionsHtml}
        </div>
    `;
}

// Hacer funciones globales - asegurarse de que todas las funciones necesarias estén disponibles globalmente
window.navigateToLevel = navigateToLevel;
window.viewQuoteDetail = viewQuoteDetail;
window.loadQuoteDetail = loadQuoteDetail;
window.displayQuoteDetail = displayQuoteDetail;
window.downloadQuotePDF = downloadQuotePDF;
window.deleteQuote = deleteQuote;
window.addToQuote = addToQuote;
window.clearQuote = clearQuote;
window.saveQuote = saveQuote;
window.generateQuote = generateQuote;
window.openQuotesList = openQuotesList;
window.loadQuotesList = loadQuotesList;
window.displayQuotesList = displayQuotesList;
window.formatDate = formatDate;

console.log('Script quote-canvas.js cargado completamente - Funciones de navegación del canvas configuradas');