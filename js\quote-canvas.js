// Variables globales para el canvas de cotización
let quoteItems = [];
let currentQuoteId = 0;
let currentQuoteLevel = 1; // 1: formulario, 2: lista, 3: detalle

// Función global para navegar entre niveles
function navigateToLevel(level) {
    console.log('Navegando al nivel:', level);
    currentQuoteLevel = level;
    
    const slideInner = document.querySelector('.quote-slide-inner');
    const backQuoteBtn = document.getElementById('backQuoteBtn');
    const quoteTitle = document.getElementById('quoteTitle');
    
    if (!slideInner) {
        console.error('Elemento quote-slide-inner no encontrado');
        return false;
    }
    
    if (!backQuoteBtn) {
        console.error('Elemento backQuoteBtn no encontrado');
        return false;
    }
    
    if (!quoteTitle) {
        console.error('Elemento quoteTitle no encontrado');
        return false;
    }
    
    // Limpiar todas las clases de navegación primero
    slideInner.classList.remove('show-list', 'show-detail');
    
    switch(level) {
        case 1:
            // Nivel 1: Formulario de cotización
            backQuoteBtn.style.display = 'none';
            quoteTitle.innerHTML = '<i class="fas fa-file-invoice-dollar"></i> Generar Cotización';
            break;
        case 2:
            // Nivel 2: Lista de cotizaciones
            slideInner.classList.add('show-list');
            backQuoteBtn.style.display = 'inline-block';
            quoteTitle.innerHTML = '<i class="fas fa-clipboard-list"></i> Lista de Cotizaciones';
            break;
        case 3:
            // Nivel 3: Detalle de cotización
            slideInner.classList.add('show-list', 'show-detail');
            backQuoteBtn.style.display = 'inline-block';
            quoteTitle.innerHTML = '<i class="fas fa-file-invoice"></i> Detalle de Cotización';
            break;
        default:
            console.error('Nivel inválido:', level);
            return false;
    }
    
    console.log('Navegación completada al nivel:', level);
    return true;
}

// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado - Inicializando canvas de cotización');
    
    // Elementos del DOM
    const quoteCanvas = document.getElementById('quoteCanvas');
    const quoteOverlay = document.getElementById('quoteOverlay');
    const openQuoteBtn = document.getElementById('openQuoteBtn');
    const closeQuoteBtn = document.getElementById('closeQuoteBtn');
    const quoteItemsContainer = document.getElementById('quoteItemsContainer');
    const quoteTotals = document.getElementById('quoteTotals');
    const slideInner = document.querySelector('.quote-slide-inner');
    const backQuoteBtn = document.getElementById('backQuoteBtn');
    const quoteTitle = document.getElementById('quoteTitle');

    // Manejo de apertura y cierre del canvas
    if (openQuoteBtn) {
        openQuoteBtn.addEventListener('click', () => {
            console.log('Botón de cotización clickeado');
            if (quoteCanvas && quoteOverlay) {
                quoteCanvas.classList.add('active');
                quoteOverlay.classList.add('active');
                console.log('Canvas de cotización activado');
            } else {
                console.error('Canvas o overlay no encontrado', {quoteCanvas, quoteOverlay});
            }
        });
    } else {
        console.warn('Botón openQuoteBtn no encontrado en el DOM');
    }

    if (closeQuoteBtn) {
        closeQuoteBtn.addEventListener('click', () => {
            closeQuoteCanvas();
        });
    }

    if (quoteOverlay) {
        quoteOverlay.addEventListener('click', () => {
            closeQuoteCanvas();
        });
    }

    // Botón de volver
    if (backQuoteBtn) {
        backQuoteBtn.addEventListener('click', () => {
            navigateToLevel(currentQuoteLevel - 1);
        });
    }

    // Función para cerrar el canvas
    function closeQuoteCanvas() {
        quoteCanvas.classList.remove('active');
        quoteOverlay.classList.remove('active');
        // Volver al primer nivel cuando se cierra
        setTimeout(() => {
            navigateToLevel(1);
        }, 300);
    }

    // Manejo de tabs
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tab = button.getAttribute('data-tab');
            
            // Remover clase active de todos los botones y contenidos
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Agregar clase active al botón y contenido seleccionado
            button.classList.add('active');
            document.getElementById(`${tab}-tab`).classList.add('active');
        });
    });

    // Búsqueda de productos en tiempo real
    const searchInput = document.getElementById('quoteProductSearch');
    const searchResults = document.getElementById('quoteSearchResults');

    if (searchInput && searchResults) {
        searchInput.addEventListener('input', debounce(function() {
            const searchTerm = this.value ? this.value.trim() : '';
            
            if (searchTerm.length < 2) {
                searchResults.innerHTML = '';
                return;
            }
            
            searchProducts(searchTerm);
        }, 300));
    }

    // Hacer las funciones globales para que puedan ser llamadas desde onclick
    window.addManualProduct = addManualProduct;
    window.clearQuote = clearQuote;
    window.saveQuote = saveQuote;
    window.generateQuote = generateQuote;
    window.openQuotesList = openQuotesList;
    window.removeQuoteItem = removeQuoteItem;
    window.updateQuoteItemQuantity = updateQuoteItemQuantity;
    window.addToQuote = addToQuote;
    window.navigateToLevel = navigateToLevel;
    window.closeQuoteCanvas = closeQuoteCanvas;
    window.addProductFromSearch = addProductFromSearch;
});

// Función para agregar producto manualmente
function addManualProduct() {
    const name = document.getElementById('manualProductName').value.trim();
    const price = parseFloat(document.getElementById('manualProductPrice').value) || 0;
    const quantity = parseInt(document.getElementById('manualProductQuantity').value) || 1;
    
    if (!name) {
        alert('Por favor ingrese el nombre del producto');
        return;
    }
    
    if (price <= 0) {
        alert('Por favor ingrese un precio válido');
        return;
    }
    
    // Crear un ID único para el producto manual
    const manualId = 'manual_' + Date.now();
    
    addItemToQuote({
        id: manualId,
        name: name,
        price: price,
        quantity: quantity,
        isManual: true
    });
    
    // Limpiar los campos
    document.getElementById('manualProductName').value = '';
    document.getElementById('manualProductPrice').value = '';
    document.getElementById('manualProductQuantity').value = '1';
}

// Función para buscar productos
function searchProducts(term) {
    const searchResults = document.getElementById('quoteSearchResults');
    if (!searchResults) {
        console.error('Elemento searchResults no encontrado');
        return;
    }
    
    searchResults.innerHTML = '<div class="loading">Buscando productos...</div>';
    
    fetch(`search_products.php?term=${encodeURIComponent(term)}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.data && data.data.length > 0) {
                displaySearchResults(data.data);
            } else if (data.products && data.products.length > 0) {
                // Fallback para formato antiguo
                displaySearchResults(data.products);
            } else {
                searchResults.innerHTML = '<div class="no-results">No se encontraron productos</div>';
            }
        })
        .catch(error => {
            console.error('Error buscando productos:', error);
            searchResults.innerHTML = '<div class="error">Error al buscar productos</div>';
        });
}

// Función para mostrar resultados de búsqueda
function displaySearchResults(products) {
    const searchResults = document.getElementById('quoteSearchResults');
    
    const resultsHTML = products.map(product => `
        <div class="search-result-item">
            <div class="product-info">
                <h4>${product.nombre}</h4>
                <p>SKU: ${product.sku} | Stock: ${product.stock_total || 0}</p>
                <p class="price">$${Number(product.precio_venta).toLocaleString('es-CL')}</p>
            </div>
            <button class="action-button small" onclick="addProductFromSearch('${product.id}', '${product.nombre.replace(/'/g, "\\'")}', ${product.precio_venta})">
                <i class="fas fa-plus"></i>
            </button>
        </div>
    `).join('');
    
    searchResults.innerHTML = resultsHTML;
}

// Función para agregar producto desde búsqueda
function addProductFromSearch(id, name, price) {
    addItemToQuote({
        id: id,
        name: name,
        price: price,
        quantity: 1,
        isManual: false
    });
}

// Función para agregar item a la cotización
function addItemToQuote(item) {
    // Verificar si el producto ya existe
    const existingItem = quoteItems.find(i => i.id === item.id);
    
    if (existingItem) {
        existingItem.quantity += item.quantity;
    } else {
        quoteItems.push(item);
    }
    
    updateQuoteCount();
    renderQuoteItems();
}

// Función para renderizar los items de la cotización
function renderQuoteItems() {
    const container = document.getElementById('quoteItemsContainer');
    const totals = document.getElementById('quoteTotals');
    
    if (quoteItems.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <p>No hay productos agregados a la cotización</p>
            </div>
        `;
        totals.style.display = 'none';
        return;
    }
    
    const itemsHTML = quoteItems.map((item, index) => `
        <div class="quote-item" data-index="${index}">
            <div class="item-info">
                <h4>${item.name}</h4>
                <p class="item-price">$${Number(item.price).toLocaleString('es-CL')} c/u</p>
            </div>
            <div class="item-controls">
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuoteItemQuantity(${index}, -1)">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuoteItemQuantity(${index}, 1)">+</button>
                </div>
                <div class="item-total">
                    $${(item.price * item.quantity).toLocaleString('es-CL')}
                </div>
                <button class="remove-btn" onclick="removeQuoteItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = itemsHTML;
    totals.style.display = 'block';
    
    // Calcular y mostrar totales
    updateTotals();
}

// Función para actualizar cantidad de un item
function updateQuoteItemQuantity(index, change) {
    const item = quoteItems[index];
    item.quantity = Math.max(1, item.quantity + change);
    renderQuoteItems();
    updateQuoteCount();
}

// Función para eliminar un item
function removeQuoteItem(index) {
    quoteItems.splice(index, 1);
    renderQuoteItems();
    updateQuoteCount();
}

// Función para actualizar totales
function updateTotals() {
    const subtotal = quoteItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    document.getElementById('quoteSubtotal').textContent = `$${subtotal.toLocaleString('es-CL')}`;
    document.getElementById('quoteTotal').textContent = `$${subtotal.toLocaleString('es-CL')}`;
}

// Función para actualizar contador en el botón
function updateQuoteCount() {
    const countElement = document.querySelector('.quote-count');
    if (countElement) {
        const totalItems = quoteItems.reduce((sum, item) => sum + item.quantity, 0);
        countElement.textContent = totalItems;
        
        // Añadir animación
        countElement.classList.add('updated');
        setTimeout(() => {
            countElement.classList.remove('updated');
        }, 300);
    }
}

// Función para limpiar la cotización
function clearQuote() {
    if (quoteItems.length === 0) return;
    
    if (confirm('¿Está seguro de que desea limpiar toda la cotización?')) {
        quoteItems = [];
        renderQuoteItems();
        updateQuoteCount();
        
        // Limpiar campos del formulario
        document.getElementById('quoteClientName').value = '';
        document.getElementById('quoteClientRut').value = '';
        document.getElementById('quoteClientEmail').value = '';
        document.getElementById('quoteClientPhone').value = '';
        document.getElementById('quoteNotes').value = '';
    }
}

// Función para guardar la cotización
function saveQuote() {
    if (quoteItems.length === 0) {
        alert('No hay productos en la cotización');
        return;
    }
    
    const clientName = document.getElementById('quoteClientName').value.trim();
    if (!clientName) {
        alert('Por favor ingrese el nombre del cliente');
        return;
    }
    
    const quoteData = {
        client: {
            name: clientName,
            rut: document.getElementById('quoteClientRut').value.trim(),
            email: document.getElementById('quoteClientEmail').value.trim(),
            phone: document.getElementById('quoteClientPhone').value.trim()
        },
        items: quoteItems,
        notes: document.getElementById('quoteNotes').value.trim(),
        total: quoteItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    };
    
    // Mostrar indicador de carga
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
    
    fetch('save_quote.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(quoteData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Cotización guardada exitosamente');
            clearQuote();
        } else {
            alert('Error al guardar la cotización: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al guardar la cotización');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-floppy-disk"></i> Guardar';
    });
}

// Función para generar PDF
function generateQuote() {
    if (quoteItems.length === 0) {
        alert('No hay productos en la cotización');
        return;
    }
    
    const clientName = document.getElementById('quoteClientName').value.trim();
    if (!clientName) {
        alert('Por favor ingrese el nombre del cliente');
        return;
    }
    
    const quoteData = {
        client: {
            name: clientName,
            rut: document.getElementById('quoteClientRut').value.trim(),
            email: document.getElementById('quoteClientEmail').value.trim(),
            phone: document.getElementById('quoteClientPhone').value.trim()
        },
        items: quoteItems,
        notes: document.getElementById('quoteNotes').value.trim(),
        total: quoteItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    };
    
    // Mostrar indicador de carga
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generando PDF...';
    
    fetch('generate_quote_pdf.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(quoteData)
    })
    .then(response => response.blob())
    .then(blob => {
        // Descargar el PDF
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cotizacion_${Date.now()}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
        
        alert('PDF generado exitosamente');
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al generar el PDF');
    })
    .finally(() => {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-file-export"></i> Generar PDF';
    });
}

// Función para abrir lista de cotizaciones
function openQuotesList() {
    // Navegar al nivel 2 (lista)
    navigateToLevel(2);
    
    // Cargar las cotizaciones
    loadQuotesList();
}

// Función para agregar a cotización desde otros lugares
function addToQuote(productId, productName, productPrice) {
    console.log('Agregando a cotización:', productId, productName, productPrice);
    
    addItemToQuote({
        id: productId,
        name: productName,
        price: parseFloat(productPrice),
        quantity: 1,
        isManual: false
    });
    
    // Mostrar notificación
    const notification = document.createElement('div');
    notification.className = 'quote-notification';
    notification.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>Producto agregado a la cotización</span>
    `;
    document.body.appendChild(notification);
    
    // Animar la notificación
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 2000);
}

// Función debounce para optimizar búsquedas
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Función para cargar lista de cotizaciones
function loadQuotesList() {
    console.log('Cargando lista de cotizaciones...');
    const container = document.getElementById('quotesListContainer');
    
    if (!container) {
        console.error('Container de lista de cotizaciones no encontrado');
        return;
    }
    
    container.innerHTML = '<div class="loading-quotes"><i class="fas fa-spinner fa-spin"></i> Cargando cotizaciones...</div>';
    
    fetch('get_quotes.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.quotes && data.quotes.length > 0) {
                displayQuotesList(data.quotes);
            } else {
                container.innerHTML = `
                    <div class="no-quotes">
                        <i class="fas fa-file-invoice"></i>
                        <p>No hay cotizaciones guardadas</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error cargando cotizaciones:', error);
            container.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error al cargar las cotizaciones</p>
                </div>
            `;
        });
}

// Función para mostrar la lista de cotizaciones
function displayQuotesList(quotes) {
    const container = document.getElementById('quotesListContainer');
    
    const quotesHTML = quotes.map(quote => `
        <div class="quote-list-item" data-id="${quote.id}">
            <div class="quote-info">
                <h4>Cotización #${quote.numero || quote.id}</h4>
                <p class="client-name">${quote.cliente_nombre || 'Sin cliente'}</p>
                <p class="quote-date">${formatDate(quote.fecha_creacion)}</p>
            </div>
            <div class="quote-amount">
                <p class="total">$${Number(quote.total || 0).toLocaleString('es-CL')}</p>
                <p class="items-count">${quote.cantidad_items || 0} items</p>
            </div>
            <div class="quote-actions">
                <button class="action-btn view" onclick="viewQuoteDetail(${quote.id})" title="Ver detalles">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn pdf" onclick="downloadQuotePDF(${quote.id})" title="Descargar PDF">
                    <i class="fas fa-file-pdf"></i>
                </button>
                <button class="action-btn delete" onclick="deleteQuote(${quote.id})" title="Eliminar">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = quotesHTML;
}

// Función para formatear fecha
function formatDate(dateString) {
    if (!dateString) return 'Sin fecha';
    const date = new Date(dateString);
    return date.toLocaleDateString('es-CL', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Función para ver detalle de cotización
function viewQuoteDetail(quoteId) {
    console.log('Ver detalle de cotización:', quoteId);
    
    // Validar que el ID sea válido
    if (!quoteId || isNaN(quoteId)) {
        console.error('ID de cotización inválido:', quoteId);
        alert('Error: ID de cotización inválido');
        return;
    }
    
    // Primero cargar los datos y luego navegar al nivel 3
    try {
        // Asegurarse de que el contenedor de detalle exista
        const container = document.getElementById('quoteDetailContent');
        if (!container) {
            console.error('Container de detalle no encontrado');
            alert('Error: Container de detalle no encontrado en el DOM');
            return;
        }
        
        // Mostrar indicador de carga
        container.innerHTML = '<div class="loading-detail"><i class="fas fa-spinner fa-spin"></i> Cargando detalle...</div>';
        
        // Navegar al nivel de detalle
        navigateToLevel(3);
        
        // Cargar el detalle
        loadQuoteDetail(quoteId);
    } catch (error) {
        console.error('Error al cargar detalle de cotización:', error);
        alert('Error al cargar el detalle de la cotización: ' + error.message);
    }
}

// Función para descargar PDF de cotización existente
function downloadQuotePDF(quoteId) {
    window.location.href = `download_quote_pdf.php?id=${quoteId}`;
}

// Función para eliminar cotización
function deleteQuote(quoteId) {
    if (!confirm('¿Está seguro de eliminar esta cotización?')) return;
    
    fetch(`delete_quote.php?id=${quoteId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadQuotesList(); // Recargar lista
        } else {
            alert('Error al eliminar la cotización');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al eliminar la cotización');
    });
}

// Función para cargar detalle de cotización
function loadQuoteDetail(quoteId) {
    console.log('Cargando detalle de cotización ID:', quoteId);
    const container = document.getElementById('quoteDetailContent');
    
    if (!container) {
        console.error('Container de detalle no encontrado');
        return;
    }
    
    // Ya no necesitamos mostrar el indicador de carga aquí porque lo hacemos en viewQuoteDetail
    
    fetch(`get_quote_detail.php?id=${quoteId}`)
        .then(response => {
            console.log('Respuesta recibida:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return response.json();
        })
        .then(data => {
            console.log('Datos recibidos:', data);
            if (data.status === 'success' && data.quote) {
                displayQuoteDetail(data.quote);
            } else {
                console.error('Error en datos:', data);
                const errorMessage = data.message || 'Error desconocido al cargar el detalle';
                container.innerHTML = `<div class="error">Error: ${errorMessage}</div>`;
            }
        })
        .catch(error => {
            console.error('Error en fetch:', error);
            container.innerHTML = `<div class="error">Error de conexión: ${error.message}</div>`;
        });
}

// Función para mostrar el detalle
function displayQuoteDetail(quote) {
    console.log('Mostrando detalle de cotización:', quote);
    const container = document.getElementById('quoteDetailContent');
    
    if (!container) {
        console.error('Container de detalle no encontrado');
        return;
    }
    
    try {
        // Verificar que los datos necesarios estén presentes
        if (!quote || !quote.items) {
            console.error('Datos de cotización incompletos:', quote);
            container.innerHTML = '<div class="error">Error: Datos de cotización incompletos</div>';
            return;
        }
        
        // Formatear fecha correctamente
        const fecha = quote.fecha || quote.fecha_creacion;
        
        const detailHTML = `
            <div class="quote-detail">
                <div class="detail-header">
                    <h3>Cotización #${quote.numero || quote.id}</h3>
                    <p class="date">${formatDate(fecha)}</p>
                </div>
                
                <div class="client-detail">
                    <h4>Información del Cliente</h4>
                    <p><strong>Nombre:</strong> ${quote.cliente_nombre || 'Sin especificar'}</p>
                    <p><strong>RUT:</strong> ${quote.cliente_rut || 'Sin especificar'}</p>
                    <p><strong>Email:</strong> ${quote.cliente_email || 'Sin especificar'}</p>
                    <p><strong>Teléfono:</strong> ${quote.cliente_telefono || 'Sin especificar'}</p>
                </div>
                
                <div class="items-detail">
                    <h4>Productos</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>Producto</th>
                                <th>Cantidad</th>
                                <th>Precio Unit.</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${quote.items.map(item => {
                                const precio = item.precio_unitario || item.precio;
                                const cantidad = item.cantidad || 1;
                                const total = precio * cantidad;
                                return `
                                <tr>
                                    <td>${item.nombre}</td>
                                    <td>${cantidad}</td>
                                    <td>$${Number(precio).toLocaleString('es-CL')}</td>
                                    <td>$${Number(total).toLocaleString('es-CL')}</td>
                                </tr>
                                `;
                            }).join('')}
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3"><strong>Total</strong></td>
                                <td><strong>$${Number(quote.total).toLocaleString('es-CL')}</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                ${quote.notas ? `
                    <div class="notes-detail">
                        <h4>Notas</h4>
                        <p>${quote.notas}</p>
                    </div>
                ` : ''}
                
                <div class="detail-actions">
                    <button class="action-button primary" onclick="downloadQuotePDF(${quote.id})">
                        <i class="fas fa-file-pdf"></i> Descargar PDF
                    </button>
                    <button class="action-button secondary" onclick="navigateToLevel(1)">
                        <i class="fas fa-arrow-left"></i> Volver
                    </button>
                </div>
            </div>
        `;
        
        container.innerHTML = detailHTML;
    } catch (error) {
        console.error('Error al mostrar detalle:', error);
        container.innerHTML = '<div class="error">Error al mostrar el detalle de la cotización</div>';
    }
}

// Hacer funciones globales - asegurarse de que todas las funciones necesarias estén disponibles globalmente
window.navigateToLevel = navigateToLevel;
window.viewQuoteDetail = viewQuoteDetail;
window.loadQuoteDetail = loadQuoteDetail;
window.displayQuoteDetail = displayQuoteDetail;
window.downloadQuotePDF = downloadQuotePDF;
window.deleteQuote = deleteQuote;
window.addToQuote = addToQuote;
window.clearQuote = clearQuote;
window.saveQuote = saveQuote;
window.generateQuote = generateQuote;
window.openQuotesList = openQuotesList;
window.loadQuotesList = loadQuotesList;
window.displayQuotesList = displayQuotesList;
window.formatDate = formatDate;

console.log('Script quote-canvas.js cargado completamente - Funciones de navegación del canvas configuradas');