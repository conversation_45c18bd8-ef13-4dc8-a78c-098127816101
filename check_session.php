<?php
// Incluir configuración de sesiones
require_once 'session_config.php';
session_start();

// Responder con JSON
header('Content-Type: application/json');

// Verificar si el usuario está logueado
$response = [
    'logged_in' => isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true,
    'username' => isset($_SESSION['username']) ? $_SESSION['username'] : null,
    'session_id' => session_id()
];

echo json_encode($response);
exit;
?>