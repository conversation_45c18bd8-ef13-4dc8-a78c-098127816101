<?php
// Configuración para depuración y manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(30);

// Headers CORS y JSON
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Cache-Control, Pragma');
header('Content-Type: application/json; charset=utf-8');

// Función para enviar respuesta JSON
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

try {
    session_start();
    require_once 'db_connection.php';

    // Verificar autenticación
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        sendJsonResponse([
            'status' => 'error',
            'message' => 'No autorizado'
        ], 401);
    }

    // Obtener término de búsqueda
    $searchTerm = isset($_GET['term']) ? trim($_GET['term']) : '';

    if (strlen($searchTerm) < 2) {
        sendJsonResponse([
            'status' => 'success',
            'data' => [],
            'message' => 'Término de búsqueda muy corto'
        ]);
    }

    $conn = getConnection();

    // Query optimizada para buscar productos con stock
    $sql = "SELECT 
                r.id,
                r.nombre,
                r.descripcion,
                r.sku,
                r.precio_venta,
                r.url_imagen,
                r.fabricante,
                c.nombre AS categoria_nombre,
                COALESCE(SUM(s.cantidad), 0) AS stock_total
            FROM repuesto r
            LEFT JOIN categoria_repuesto c ON r.categoria_id = c.id
            LEFT JOIN stock s ON s.repuesto_id = r.id
            WHERE r.activo = 1
            AND (
                r.nombre LIKE :search1
                OR r.sku LIKE :search2
                OR r.descripcion LIKE :search3
                OR r.codigo_fabricante LIKE :search4
                OR r.fabricante LIKE :search5
            )
            GROUP BY r.id
            ORDER BY 
                CASE 
                    WHEN r.sku = :exact_term THEN 1
                    WHEN r.sku LIKE :start_term THEN 2
                    ELSE 3
                END,
                r.nombre ASC
            LIMIT 20";
    
    $stmt = $conn->prepare($sql);
    $searchPattern = '%' . $searchTerm . '%';
    $startPattern = $searchTerm . '%';
    
    $stmt->execute([
        'search1' => $searchPattern,
        'search2' => $searchPattern,
        'search3' => $searchPattern,
        'search4' => $searchPattern,
        'search5' => $searchPattern,
        'exact_term' => $searchTerm,
        'start_term' => $startPattern
    ]);
    
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    sendJsonResponse([
        'status' => 'success',
        'data' => $products,
        'count' => count($products),
        'search_term' => $searchTerm
    ]);
    
} catch (Exception $e) {
    sendJsonResponse([
        'status' => 'error',
        'message' => 'Error al buscar productos: ' . $e->getMessage()
    ], 500);
}
?>
