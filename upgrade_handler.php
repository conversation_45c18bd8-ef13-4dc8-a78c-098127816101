<?php
// Aumentar límite de tiempo de ejecución
set_time_limit(300); // 5 minutos
ini_set('max_execution_time', 300);

// Aumentar límite de memoria si es necesario
ini_set('memory_limit', '256M');

// Iniciar buffer de salida
ob_start();

// Incluir archivos necesarios
require_once 'db_connection.php';

// Configurar manejo de errores
error_reporting(E_ALL);
ini_set('display_errors', 0); // No mostrar errores en la salida pero sí registrarlos

// Array para almacenar mensajes de depuración
$debugMessages = [];

// Función para registrar mensajes de depuración
function debugLog($message, $type = 'info') {
    global $debugMessages;

    // Registrar en el log del servidor
    write_log($message);

    // Guardar para mostrar en la respuesta JSON
    $timestamp = date('H:i:s');
    $debugMessages[] = [
        'time' => $timestamp,
        'message' => $message,
        'type' => $type
    ];

    return $message;
}

// Asegurarse de que no hay salida antes de los encabezados
ob_clean();

// Establecer encabezados para evitar el caché y especificar el tipo de contenido
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }

    // Verificar si es una petición AJAX
    $isAjax = isset($_POST['ajax_request']) && $_POST['ajax_request'] === 'true';

    // Verificar si es una solicitud de registrar upgrade
    // Aceptamos la solicitud incluso si el parámetro registrar_upgrade no está presente
    // siempre que sea una petición AJAX desde el formulario
    if (!isset($_POST['registrar_upgrade']) && !$isAjax) {
        debugLog('Operación no especificada: falta el parámetro registrar_upgrade', 'error');
        debugLog('POST recibido: ' . print_r($_POST, true), 'error');
        throw new Exception('Operación no especificada');
    }

    // Si es una petición AJAX, asumimos que es para registrar un upgrade
    if ($isAjax) {
        debugLog('Petición AJAX detectada, asumiendo operación de registro de upgrade', 'info');
    }

    // Registrar los datos recibidos para depuración
    debugLog('Datos POST recibidos: ' . print_r($_POST, true));
    debugLog('Archivos recibidos: ' . print_r($_FILES, true));

    // Validar campos requeridos
    if (empty($_POST['pagina_modificada']) || empty($_POST['descripcion'])) {
        throw new Exception('Todos los campos son obligatorios');
    }

    $paginaModificada = $_POST['pagina_modificada'];
    $descripcion = $_POST['descripcion'];
    $rutaArchivo = null;
    $tipoArchivo = 'ninguno';

    // Procesar la imagen si fue enviada
    debugLog('Verificando archivo subido...');
    if (isset($_FILES['archivo']) && $_FILES['archivo']['error'] === UPLOAD_ERR_OK) {
        debugLog('Archivo encontrado: ' . print_r($_FILES['archivo'], true));

        $fileName = $_FILES['archivo']['name'];
        $fileType = $_FILES['archivo']['type'];
        $fileSize = $_FILES['archivo']['size'];
        $fileTmpName = $_FILES['archivo']['tmp_name'];
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        debugLog('Nombre del archivo: ' . $fileName);
        debugLog('Tipo del archivo: ' . $fileType);
        debugLog('Tamaño del archivo: ' . $fileSize);
        debugLog('Archivo temporal: ' . $fileTmpName);
        debugLog('Extensión del archivo: ' . $fileExtension);

        // Verificar si el archivo es una imagen o un video
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $videoExtensions = ['mp4', 'webm', 'ogg', 'mov', 'avi'];

        // Determinar el tipo de archivo
        if (in_array($fileExtension, $imageExtensions)) {
            debugLog('Archivo identificado como imagen', 'success');
            $tipoArchivo = 'imagen';
        } elseif (in_array($fileExtension, $videoExtensions)) {
            debugLog('Archivo identificado como video', 'success');
            $tipoArchivo = 'video';
        } else {
            throw new Exception('Tipo de archivo no soportado. Solo se permiten imágenes y videos.');
        }

        // Directorio para guardar archivos
        $uploadDir = 'images/upgrades/';
        $absoluteUploadDir = __DIR__ . '/' . $uploadDir;

        // Verificar y crear directorio si no existe
        if (!file_exists($absoluteUploadDir)) {
            if (!mkdir($absoluteUploadDir, 0777, true)) {
                throw new Exception('No se pudo crear el directorio para guardar el archivo');
            }
        }

        // Generar nombre único para el archivo
        $uniqueId = uniqid();
        $timestamp = time();
        $safeFilename = preg_replace('/[^\w\-\.]/', '_', pathinfo($fileName, PATHINFO_FILENAME));
        $newFileName = 'upgrade_' . $safeFilename . '_' . $uniqueId . '_' . $timestamp . '.' . $fileExtension;
        $filePath = $absoluteUploadDir . $newFileName;

        debugLog('Ruta del archivo a guardar: ' . $filePath);

        // Mover el archivo subido
        if (!move_uploaded_file($fileTmpName, $filePath)) {
            throw new Exception('Error al guardar el archivo');
        }

        // Asignar la ruta relativa para guardar en la BD
        $rutaArchivo = $uploadDir . $newFileName;
        debugLog('Ruta relativa guardada: ' . $rutaArchivo);
    }

    // Conectar a la base de datos
    try {
        $conn = getConnection();

        // Debug database connection
        debugLog('Conexión a la base de datos establecida', 'success');
        debugLog('Información de la conexión: ' . print_r($conn, true));

        // Test query to verify connection
        $testQuery = $conn->query('SELECT 1');
        if ($testQuery) {
            debugLog('Prueba de consulta exitosa', 'success');
        } else {
            debugLog('Error en prueba de consulta', 'error');
        }

        // Verificar si la tabla tb_upgrades existe
        $checkTableSql = "SHOW TABLES LIKE 'tb_upgrades'";
        $checkTableStmt = $conn->query($checkTableSql);
        $tableExists = $checkTableStmt->rowCount() > 0;

        // Crear tabla si no existe
        if (!$tableExists) {
            $createTableSql = "
                CREATE TABLE tb_upgrades (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
                    pagina_modificada VARCHAR(100) NOT NULL,
                    descripcion TEXT NOT NULL,
                    ruta_archivo VARCHAR(255),
                    tipo_archivo ENUM('imagen', 'video', 'ninguno') DEFAULT 'ninguno',
                    usuario VARCHAR(100) DEFAULT 'admin'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            $conn->exec($createTableSql);
            debugLog('Tabla tb_upgrades creada');
        }

        // Verificar si la conexión sigue activa
        if (!$conn) {
            throw new Exception('La conexión a la base de datos se perdió');
        }

        // Preparar la consulta SQL
        $sql = "INSERT INTO tb_upgrades (pagina_modificada, descripcion, ruta_archivo, tipo_archivo)
                VALUES (:pagina_modificada, :descripcion, :ruta_archivo, :tipo_archivo)";

        $stmt = $conn->prepare($sql);

        // Vincular parámetros
        $stmt->bindParam(':pagina_modificada', $paginaModificada);
        $stmt->bindParam(':descripcion', $descripcion);
        $stmt->bindParam(':ruta_archivo', $rutaArchivo);
        $stmt->bindParam(':tipo_archivo', $tipoArchivo);

        debugLog('Parámetros a insertar:');
        debugLog('- pagina_modificada: ' . $paginaModificada);
        debugLog('- descripcion: ' . $descripcion);
        debugLog('- ruta_archivo: ' . ($rutaArchivo ?: 'NULL'));
        debugLog('- tipo_archivo: ' . $tipoArchivo);

        // Ejecutar la consulta
        if (!$stmt->execute()) {
            $errorInfo = $stmt->errorInfo();
            throw new Exception('Error al guardar en la base de datos: ' . $errorInfo[2]);
        }

        // Obtener el ID del registro insertado
        $lastId = $conn->lastInsertId();
        debugLog('Registro insertado con ID: ' . $lastId);

        // Preparar respuesta
        $response = [
            'status' => 'success',
            'message' => 'Registro de upgrade guardado correctamente',
            'id' => $lastId,
            'data' => [
                'pagina_modificada' => $paginaModificada,
                'descripcion' => $descripcion,
                'ruta_archivo' => $rutaArchivo,
                'tipo_archivo' => $tipoArchivo
            ],
            'debug_messages' => $debugMessages
        ];

        // Verificar si el registro se insertó consultando la base de datos
        try {
            $checkStmt = $conn->prepare("SELECT * FROM tb_upgrades WHERE id = :id");
            $checkStmt->bindParam(':id', $lastId);
            $checkStmt->execute();
            $record = $checkStmt->fetch(PDO::FETCH_ASSOC);

            if ($record) {
                debugLog('Verificación: registro encontrado en la base de datos con ID ' . $lastId, 'success');
                $response['verification'] = [
                    'success' => true,
                    'record' => $record
                ];
            } else {
                debugLog('Verificación: registro NO encontrado en la base de datos a pesar del éxito de la inserción', 'warning');
                $response['verification'] = [
                    'success' => false,
                    'message' => 'No se encontró el registro a pesar de reportar éxito en la inserción'
                ];
            }
        } catch (Exception $e) {
            debugLog('Error al verificar el registro: ' . $e->getMessage(), 'error');
            $response['verification'] = [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }

        // Si hay debug_info, incluirla en la respuesta
        if (isset($_POST['debug_info']) && $_POST['debug_info'] === '1') {
            $response['debug'] = [
                'post_data' => $_POST,
                'files' => isset($_FILES) ? $_FILES : null,
                'server' => $_SERVER['REQUEST_METHOD'],
                'time' => date('Y-m-d H:i:s'),
                'memory_usage' => memory_get_usage(true) / 1024 / 1024 . ' MB'
            ];
        }

        echo json_encode($response);

    } catch (PDOException $e) {
        throw new Exception('Error de base de datos: ' . $e->getMessage());
    }

    // Cerrar la conexión
    if (isset($conn)) {
        $conn = null;
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'debug' => [
            'error_type' => get_class($e),
            'error_line' => $e->getLine(),
            'error_file' => $e->getFile()
        ]
    ]);
}

// Limpiar y cerrar el buffer de salida
ob_end_flush();
