<?php
session_start();
require_once 'db_connection.php';

// Inicializar variables
$error = '';
$success = false;
$token = '';
$validToken = false;
$userId = null;

// Verificar si se proporcionó un token
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];

    try {
        $conn = getConnection();

        // Verificar si el token existe y no ha expirado
        $checkTokenSql = "SELECT id, username FROM tb_usuarios
                          WHERE token_recuperacion = ?
                          AND expiracion_token > NOW()";
        $checkStmt = $conn->prepare($checkTokenSql);
        $checkStmt->execute([$token]);
        $user = $checkStmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            $validToken = true;
            $userId = $user['id'];
        } else {
            $error = 'El enlace de recuperación no es válido o ha expirado.';
        }
    } catch (Exception $e) {
        $error = 'Error en el servidor. Por favor, intente nuevamente más tarde.';
        write_log('Error en reset_password.php (verificación de token): ' . $e->getMessage());
    }
} else {
    $error = 'No se proporcionó un token de recuperación.';
}

// Procesar el formulario si se envió
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $validToken) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validar las contraseñas
    if (empty($password) || empty($confirm_password)) {
        $error = 'Por favor, complete todos los campos.';
    } elseif ($password !== $confirm_password) {
        $error = 'Las contraseñas no coinciden.';
    } elseif (strlen($password) < 8) {
        $error = 'La contraseña debe tener al menos 8 caracteres.';
    } else {
        try {
            $conn = getConnection();

            // Actualizar la contraseña y limpiar el token
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $updateSql = "UPDATE tb_usuarios
                          SET password = ?,
                              token_recuperacion = NULL,
                              expiracion_token = NULL
                          WHERE id = ?";
            $updateStmt = $conn->prepare($updateSql);
            $result = $updateStmt->execute([$hashedPassword, $userId]);

            if ($result) {
                $success = true;

                // Establecer mensaje de éxito para mostrar en login.php
                $_SESSION['success_message'] = 'Su contraseña ha sido restablecida exitosamente. Ahora puede iniciar sesión con su nueva contraseña.';

                // Redirigir a login.php después de 3 segundos
                header('Refresh: 3; URL=login.php');
            } else {
                $error = 'Error al restablecer la contraseña. Por favor, intente nuevamente.';
            }
        } catch (Exception $e) {
            $error = 'Error en el servidor. Por favor, intente nuevamente más tarde.';
            write_log('Error en reset_password.php (actualización de contraseña): ' . $e->getMessage());
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restablecer Contraseña - TATA REPUESTOS</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #e74c3c;
            --accent-color: #3498db;
            --hover-color: #e67e22;
            --error-color: #e74c3c;
            --success-color: #2ecc71;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), #34495e);
        }

        .reset-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
        }

        .logo {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 700;
        }

        .logo i {
            color: var(--secondary-color);
            margin-right: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .password-container {
            position: relative;
        }

        .toggle-password {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #777;
        }

        .password-strength {
            margin-top: 5px;
            font-size: 0.8rem;
            color: #777;
        }

        .password-strength-meter {
            height: 5px;
            width: 100%;
            background-color: #eee;
            margin-top: 5px;
            border-radius: 3px;
            overflow: hidden;
        }

        .password-strength-meter div {
            height: 100%;
            width: 0;
            transition: width 0.3s ease;
        }

        .strength-weak {
            background-color: var(--error-color);
        }

        .strength-medium {
            background-color: #f39c12;
        }

        .strength-strong {
            background-color: var(--success-color);
        }

        .btn {
            width: 100%;
            padding: 0.8rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--hover-color);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
            margin-top: 1rem;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .error-message {
            background-color: rgba(231, 76, 60, 0.1);
            border-left: 4px solid var(--error-color);
            color: var(--error-color);
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
            font-size: 0.9rem;
        }

        .success-message {
            background-color: rgba(46, 204, 113, 0.1);
            border-left: 4px solid var(--success-color);
            color: var(--success-color);
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
            font-size: 0.9rem;
        }

        .instructions {
            margin-bottom: 1.5rem;
            color: #555;
            font-size: 0.95rem;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="logo">
            <i class="fas fa-cogs"></i>
            Tata repuestos
        </div>

        <?php if (!empty($error)): ?>
        <div class="error-message">
            <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="success-message">
            <i class="fas fa-check-circle"></i> Su contraseña ha sido restablecida exitosamente. Redirigiendo al inicio de sesión...
        </div>
        <?php elseif ($validToken): ?>

        <div class="instructions">
            <p>Por favor, ingrese su nueva contraseña.</p>
        </div>

        <form action="reset_password.php?token=<?php echo htmlspecialchars($token); ?>" method="POST" id="resetForm">
            <div class="form-group">
                <label for="password">Nueva contraseña</label>
                <div class="password-container">
                    <input type="password" id="password" name="password" required onkeyup="checkPasswordStrength()">
                    <i class="toggle-password fas fa-eye" onclick="togglePasswordVisibility('password')"></i>
                </div>
                <div class="password-strength">
                    <span id="password-strength-text">Fuerza de la contraseña</span>
                    <div class="password-strength-meter">
                        <div id="password-strength-meter-bar"></div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="confirm_password">Confirmar nueva contraseña</label>
                <div class="password-container">
                    <input type="password" id="confirm_password" name="confirm_password" required onkeyup="checkPasswordMatch()">
                    <i class="toggle-password fas fa-eye" onclick="togglePasswordVisibility('confirm_password')"></i>
                </div>
                <div id="password-match" style="margin-top: 5px; font-size: 0.8rem;"></div>
            </div>

            <div style="display: flex; flex-direction: column; gap: 10px;">
                <button type="submit" class="btn btn-primary">Restablecer contraseña</button>
                <a href="login.php" class="btn btn-secondary" style="text-align: center; text-decoration: none;">Volver al inicio de sesión</a>
            </div>
        </form>
        <?php else: ?>
        <div class="instructions">
            <p>El enlace de recuperación no es válido o ha expirado. Por favor, solicite un nuevo enlace de recuperación.</p>
        </div>
        <div style="display: flex; flex-direction: column; gap: 10px;">
            <a href="forgot_password.php" class="btn btn-primary" style="text-align: center; text-decoration: none;">Solicitar nuevo enlace</a>
            <a href="login.php" class="btn btn-secondary" style="text-align: center; text-decoration: none;">Volver al inicio de sesión</a>
        </div>
        <?php endif; ?>
    </div>

    <script>
    function togglePasswordVisibility(inputId) {
        const passwordInput = document.getElementById(inputId);
        const toggleIcon = passwordInput.nextElementSibling;

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    function checkPasswordStrength() {
        const password = document.getElementById('password').value;
        const strengthMeter = document.getElementById('password-strength-meter-bar');
        const strengthText = document.getElementById('password-strength-text');

        // Limpiar clases anteriores
        strengthMeter.className = '';

        // Verificar la fuerza de la contraseña
        let strength = 0;

        // Longitud mínima
        if (password.length >= 8) strength += 1;

        // Contiene letras minúsculas y mayúsculas
        if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 1;

        // Contiene números
        if (password.match(/[0-9]/)) strength += 1;

        // Contiene caracteres especiales
        if (password.match(/[^a-zA-Z0-9]/)) strength += 1;

        // Actualizar el medidor y el texto según la fuerza
        switch (strength) {
            case 0:
                strengthMeter.style.width = '0%';
                strengthText.textContent = 'Fuerza de la contraseña';
                break;
            case 1:
                strengthMeter.style.width = '25%';
                strengthMeter.classList.add('strength-weak');
                strengthText.textContent = 'Débil';
                strengthText.style.color = '#e74c3c';
                break;
            case 2:
                strengthMeter.style.width = '50%';
                strengthMeter.classList.add('strength-medium');
                strengthText.textContent = 'Media';
                strengthText.style.color = '#f39c12';
                break;
            case 3:
                strengthMeter.style.width = '75%';
                strengthMeter.classList.add('strength-medium');
                strengthText.textContent = 'Buena';
                strengthText.style.color = '#f39c12';
                break;
            case 4:
                strengthMeter.style.width = '100%';
                strengthMeter.classList.add('strength-strong');
                strengthText.textContent = 'Fuerte';
                strengthText.style.color = '#2ecc71';
                break;
        }

        // También verificar si las contraseñas coinciden
        checkPasswordMatch();
    }

    function checkPasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const matchText = document.getElementById('password-match');

        if (confirmPassword.length === 0) {
            matchText.textContent = '';
            matchText.style.color = '';
            return;
        }

        if (password === confirmPassword) {
            matchText.textContent = 'Las contraseñas coinciden';
            matchText.style.color = '#2ecc71';
        } else {
            matchText.textContent = 'Las contraseñas no coinciden';
            matchText.style.color = '#e74c3c';
        }
    }

    // Validar el formulario antes de enviar
    document.getElementById('resetForm')?.addEventListener('submit', function(event) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        if (password !== confirmPassword) {
            event.preventDefault();
            alert('Las contraseñas no coinciden');
        }

        if (password.length < 8) {
            event.preventDefault();
            alert('La contraseña debe tener al menos 8 caracteres');
        }
    });
    </script>
</body>
</html>
